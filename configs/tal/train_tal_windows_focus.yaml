CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: tal_windows_focus,
   cv: 0,
   crop_size: "512,512",
   scale_max: 1.0,
   class_uniform_pct: 1,
   class_uniform_tile: 128,
   bs_trn: 8,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   brt_aug: true,
   max_epoch: 4000,
   dump_assets: true,
   dump_all_images: false,
   supervised_mscale_loss_wt: 0.05,
   val_start: 0,
   quick_val_pct: 0.25,
   full_val_every: 5,
   arch: ocrnet.HRNet_Mscale,
   result_dir: /mnt/data/projects/hrnet/outputs/train_tal/2025-06-11_tal_windows_focus_2,
   resume: "/mnt/data/projects/hrnet/outputs/train_tal/2025-06-11_tal_windows_focus/best_checkpoint_ep337.pth"
  },
]