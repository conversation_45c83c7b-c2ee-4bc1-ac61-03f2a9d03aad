# Train furniture with HRNET and HRNET trunk
# Only requires 16GB gpus

CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: tal,
   cv: 0,
   crop_size: "580,580",
   scale_max: "1.5",
   class_uniform_pct: 1,
   class_uniform_tile: 128,
   bs_trn: 1,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   max_epoch: 1000,
   dump_assets: true,
   dump_all_images: false,
   n_scales: '0.5,1.0,1.5',
   supervised_mscale_loss_wt: 0.05,
   arch: ocrnet.HRNet_Mscale,
   result_dir: /mnt/data/projects/hrnet/outputs/train_tal/2025-03-24_train-tal_ressume_after_crash,
   resume: "/mnt/data/projects/hrnet/outputs/train_tal/2025-03-24_train-tal_ressume_after_crash/last_checkpoint_ep167.pth",
   rand_augment: "2,10",
   dump_augmentation_images: false
  },
]
