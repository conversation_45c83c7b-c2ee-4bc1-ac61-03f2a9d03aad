# Train furniture with HRNET and HRNET trunk
# Only requires 16GB gpus

CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: tal_windows,
   cv: 0,
   crop_size: "512,512",
   scale_max: "1.5",
   bs_trn: 8,
   bs_val: 1,
   poly_exp: 2,
   lr: 1e-3,
   max_epoch: 300,
   hardnm: 2,
   class_uniform_tile: 128,
   class_uniform_pct: 0.7,
   dump_assets: true,
   dump_all_images: false,
   n_scales: '0.75,1.0,1.25',
   multi_scale_inference: true,
   do_flip: true,
   conf_thresh: 0.45,
   class_weights: /mnt/data/projects/hrnet/configs/tal/class_weights3.pt,
   supervised_mscale_loss_wt: 0.05,
   arch: ocrnet.HRNet_Mscale,
   result_dir: /mnt/data/projects/hrnet/outputs/train_tal/2025-05-28_train-tal-windows_more,
   resume: "/mnt/data/projects/hrnet/outputs/train_tal/2025-05-26_train-tal-windows/best_checkpoint_ep96.pth",
   rand_augment: "2,5",
   dump_augmentation_images: false,
   aux_fg_loss: true,
   aux_fg_wt:   0.2,
   label_smoothing: 0.05,
   tta_extra_scales: '0.75,1.25',
  },
]
