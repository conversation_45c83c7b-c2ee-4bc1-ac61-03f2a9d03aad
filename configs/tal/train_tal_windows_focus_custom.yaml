CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: tal_windows_focus_aug,
   cv: 0,
   crop_size: "512,512",
   scale_max: 1.5,
   class_uniform_pct: 0.7,
   class_uniform_tile: 128,
   bs_trn: 8,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   brt_aug: true,
   max_epoch: 4000,
   dump_assets: true,
   dump_all_images: false,
   n_scales: '0.75,1.0,1.25',
   multi_scale_inference: true,
   do_flip: true,
   conf_thresh: 0.45,
   class_weights: /mnt/data/projects/hrnet/configs/tal/class_weights_focus_aug.pt,
   supervised_mscale_loss_wt: 0.05,
   val_start: 0,
   quick_val_pct: 0.25,
   full_val_every: 5,
   aux_fg_loss: true,
   aux_fg_wt:   0.2,
   label_smoothing: 0.05,
   tta_extra_scales: '0.75,1.25',
   rand_augment: "2,5",
   arch: ocrnet.HRNet_Mscale,
   result_dir: /mnt/data/projects/hrnet/outputs/train_tal/2025-06-12_tal_windows_focus_custom_aug,
   resume: "/mnt/data/projects/hrnet/outputs/train_tal/2025-06-11_tal_windows_focus_custom_2/best_checkpoint_ep130.pth"
  },
]