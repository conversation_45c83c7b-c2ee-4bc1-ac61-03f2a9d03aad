# Run Evaluation on Mapillary with a pretrained model

CMD: "python train.py"

HPARAMS: [
  {
   dataset: wsp,
   cv: 0,
   bs_val: 1,
   eval: folder,
   eval_folder: "/mnt/shared/projects/cws/04_software/database/05_reviewed/package_05",
   n_scales: "1.0",
   #dump_topn: 50,
   dump_assets: true,
   dump_all_images: true,
   autolabel: true,
   snapshot: "/mnt/data/projects/hrnet/models/20220908_wsp_falloffkaputze.pth",
   arch: ocrnet.HRNet_Mscale,
   result_dir: LOGDIR
  },
]