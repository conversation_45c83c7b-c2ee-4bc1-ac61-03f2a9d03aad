# Train furniture with HRNET and HRNET trunk
# Only requires 16GB gpus

CMD: "python train.py"

HPARAMS: [
  {
   dataset: wsp,
   cv: 0,
   crop_size: "256,256",
   #256,256
   scale_max: "1.5",
   class_uniform_pct: 1,
   class_uniform_tile: 128,
   bs_trn: 16,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   max_epoch: 1000,
   #resume: "models/20211127_wsp.pth",
   dump_assets: true,
   dump_all_images: true,
   n_scales: "0.5",
   supervised_mscale_loss_wt: 0.05,
   arch: ocrnet.HRNet_Mscale,
   result_dir: LOGDIR,
   RUNX.TAG: '{arch}',
  },
]
