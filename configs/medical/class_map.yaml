# Medical defects class map for HRNet
# Format: label: [[B, G, R], "type", thickness, draw_text, min_confidence]

__background__: [[0, 0, 0], "background", 1, false, 0.4]  # Black
hole: [[0, 0, 128], "garment_defect", 1, false, 0.4]  # Dark Blue
strips: [[128, 0, 0], "garment_defect", 1, false, 0.4]  # Dark Red
stain: [[0, 128, 128], "garment_defect", 1, false, 0.4]  # Teal
line: [[0, 128, 0], "garment_defect", 1, false, 0.4]  # Dark Green
knots: [[128, 0, 128], "garment_defect", 1, false, 0.4]  # Purple
fiber: [[128, 128, 0], "garment_defect", 1, false, 0.4]  # Olive
surface: [[128, 128, 128], "garment_defect", 1, false, 0.4]  # Grey
elastic: [[255, 0, 128], "garment_defect", 1, false, 0.4]  # Bright Pink
empty: [[255, 0, 128], "garment_defect", 1, false, 0.4]  # Bright Pink
pull: [[100, 100, 255], "garment_defect", 1, false, 0.4]
seam: [[255, 128, 0], "garment_defect", 1, false, 0.4]
band: [[255, 255, 0], "garment_defect", 1, false, 0.4]
pilling: [[0, 255, 255], "garment_defect", 1, false, 0.4]
knot: [[255, 0, 255], "garment_defect", 1, false, 0.4]
platine: [[0, 255, 0], "garment_defect", 1, false, 0.4]
"colouring fold": [[0, 0, 255], "garment_defect", 1, false, 0.4]
