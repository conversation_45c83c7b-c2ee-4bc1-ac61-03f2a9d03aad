# Run Evaluation on Mapillary with a pretrained model

CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: defis_medical,
   cv: 0,
   bs_val: 1,
   eval: folder,
   eval_folder: "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_08/images",
   n_scales: ['0.5,1.0,1.5'],
   #dump_topn: 50,
   dump_assets: false,
   dump_all_images: false,
   autolabel: true,
   snapshot: "/mnt/data/projects/hrnet/outputs/train_defis-medical/2024-07-01_train-defis-medical/last_checkpoint_ep3470.pth",
   arch: ocrnet.HRNet_Mscale,
   result_dir: "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_08/output",
   label_list: "/mnt/data/projects/hrnet/helper/defis-medical_labels.txt"
  },
]