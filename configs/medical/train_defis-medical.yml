# Train furniture with HRNET and HRNET trunk
# Only requires 16GB gpus

CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: defis_medical,
   cv: 0, 
   crop_size: "1024,1024",
   scale_max: "1.0",
   val_crop: true,
   val_crop_size: "1024,1024",
   class_uniform_pct: 0.7,
   class_uniform_tile: 128,
   bs_trn: 2,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   max_epoch: 6000,
   dump_assets: true,
   dump_all_images: false,
   n_scales: "0.25,1.0",
   supervised_mscale_loss_wt: 0.05,
   val_start: 0,
   quick_val_pct: 0.25,
   full_val_every: 5,
   arch: ocrnet.HRNet_Mscale,
   result_dir: outputs/train_defis-medical/2025-07-09_train-defis-medical,
   resume: "",
   rand_augment: "3,5",
   dump_augmentation_images: false
  },
]
