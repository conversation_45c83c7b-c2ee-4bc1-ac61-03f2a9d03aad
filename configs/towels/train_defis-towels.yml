# Train furniture with HRNET and HRNET trunk
# Only requires 16GB gpus

CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: defis_towels,
   cv: 0,
   crop_size: "512,512",
   scale_max: "1.0",
   class_uniform_pct: 1,
   class_uniform_tile: 128,
   bs_trn: 6,
   bs_val: 1,
   poly_exp: 2,
   lr: 5e-3,
   rmi_loss: true,
   max_epoch: 4000,
   dump_assets: true,
   dump_all_images: false,
   n_scales: ['1.0'],
   supervised_mscale_loss_wt: 0.05,
   arch: ocrnet.HRNet_Mscale,
   result_dir: LOGDIR,
   RUNX.TAG: '{arch}',
  },
]
