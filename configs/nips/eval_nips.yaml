CMD: "python /mnt/data/projects/hrnet/train.py"

HPARAMS: [
  {
   dataset: nips,
   cv: 0,
   bs_val: 1,
   eval: test,
   eval_folder: "/mnt/datasets/nips/hrnet/test/images",
   n_scales: 0.5,1.0,
   # dump_topn: 50,
   test_mode: false,
   dump_assets: true,
   dump_all_images: false,
   autolabel: false,
   snapshot: "/mnt/data/projects/hrnet/outputs/train_nips/2025-04-28_from-scratch/best_checkpoint_ep11.pth",
   arch: ocrnet.HRNet_Mscale,
   result_dir: 'outputs/eval_nips/2025-04-30_eval-nips_from-scratch/',
   class_map_path: /mnt/data/projects/hrnet/configs/nips/class_map.yaml,
  },
]