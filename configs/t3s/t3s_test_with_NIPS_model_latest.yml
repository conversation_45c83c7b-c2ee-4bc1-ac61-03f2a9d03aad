# Run Evaluation on Mapillary with a pretrained model

CMD: "python train.py"

HPARAMS: [
  {
   dataset: nips,
   cv: 0,
   bs_val: 1,
   eval: folder,
   eval_folder: "/mnt/data/datasets/t3s/cloths_with_defects/",
   n_scales: "1.0",
   #dump_topn: 50,
   dump_assets: true,
   dump_all_images: true,
   autolabel: true,
   snapshot: "/mnt/data/projects/hrnet/logs/train_cws/ocrnet.HRNet_Mscale_therapeutic-goose_2023.10.18_22.36/model.pth",
   arch: ocrnet.HRNet_Mscale,
   result_dir: "/home/<USER>/tmp/results/",
   label_list: "/mnt/data/projects/hrnet/helper/nips_labels.txt"
  },
]
