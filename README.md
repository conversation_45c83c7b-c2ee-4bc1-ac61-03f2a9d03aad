# Material segmentation for recommender systems 


## Installation:
- Clone repository
- Install requirements.txt 

## Download Weights:
- Create a directory where you can keep large files. Ideally, not in this directory.
```bash
 > mkdir <large_asset_dir> #in the example below named "data"
```
- Update __C.ASSETS_PATH in config.py to point at that directory
```python
__C.ASSETS_PATH=<large_asset_dir>
```

* Download pretrained weights from [google drive](https://drive.google.com/open?id=1fs-uLzXvmsISbS635eRZCc5uzQdBIZ_U) and put into `<large_asset_dir>/seg_weights`



There are three type of files involved in a training:

Source image. It must be in JPEG format. The images stored by WSP are in PNG format, so it would be necessary to change the format before run a training

JSON annotation. This is the annotation file from labelme.

Grayscale mask. PNG image where value 0 represent the first class, 1 the second and so on. Usually is displayed a black image in the image viewer. It is created form a JSON annotation.

First, we run labelme2voc.py script  as follows:

python helper/labelme2voc.py data/wsp/orig/ data/wsp/out --labels data/wsp/wsp-classes.txt

where data/wsp/orig is the source folder that contains the json and the original png files. The destination folder data/wsp/out is created automatically, and it cannot exist; it contains the folder JPEGImages and the folder SegmentationClassPNG with the masks corresponding to the mentioned images. These masks appear in a .png black image with different colors according to the class of the mask. The file wsp-classes.txt has to be the following structure:
__ignore__
_background_
reflex_stripe
reflex_normal
reflex_prismen
damaged
hose
jacke
latzhose
t-shirt

language-text

labelme2voc output

This process usually takes around 30 minutes for around 1500 images

Secondly, we convert the color mask images to grayscale images by running the converter_label.py script as follows:

python helper/converter_label.py --path data/wsp/out/SegmentationClassPNG/ --path_save data/wsp/out/SegmenationClassGray

Where data/wsp/out/SegmentationClassPNG/ are the png mask and data/wsp/out/SegmenationClassGray is the output path (is create automatically)

You have to make sure that the color map given is correct. In the future, convert_label.py must be updated to get the map from the config file. In the meantime open the file and check that the variable color2index is correct

color2index = {
    (0,0,0): 0, #background
    (0, 128, 0): 1, #reflex_stripe
    (0, 0, 128) : 2, #reflex_normal
    (128, 128, 0) : 3, #reflex_prismen
    (128, 0, 0) : 4, #damaged
    (128, 0, 128): 5, #hose
    (0, 128, 128): 6, #jacke
    (128, 128, 128): 7, #latzhose
    (64, 0, 0): 8, #tshirt
}

language-python

The grayscale images can be found under dst_folder_gray. Do not forget to verify the colors are the expected

## Prepare Data:
Create your data and put your Data according to the following **structure**:
```
├── data
│   ├── wsp
│   │   ├── train
│   │   │   ├── images #all your original images .jpg
│   │   │   ├── seg #all the masks named like the original image .png Needs to be grayscale mask. (0 representing first class 1 second class...)
│   │   ├── eval
│   │   │   ├── images #all your original images .jpg
│   │   │   ├── seg #all the masks named like the original image .png
│   │   ├── test
│   │   │   ├── images #all your original images .jpg
│   │   │   ├── seg #all the masks named like the original image .png
├── semantic-segmentation-main
│   ├── ...
```
To set the path to your dataset update `config.py` to set the path::
```python
__C.DATASET.**YOURDATASET**_DIR=<path_to_your_dataset>
```
For more information about the Dataset setup check [Datasets](https://146.140.228.23:30443/desion/frs-material_recognition/-/blob/master/PREPARE_DATASETS.md).

## Prepare config:
You need to change the .runx file if you are using it. Change
`LOGROOT:`
To your logs directory.

## Run the code:
The instructions below make use of a tool called `runx`, which we find useful to help automate experiment running and summarization. For more information about this tool, please see [runx](https://github.com/NVIDIA/runx).
In general, you can either use the runx-style commandlines shown below. Or you can call `python train.py <args ...>` directly if you like.

### Run inference on Furniture
Dry run:
```bash
> python -m runx.runx scripts/eval_furniture.yml -i -n
```
This will just print out the command but not run. It's a good way to inspect the commandline. 
Real run:
```bash
> python -m runx.runx scripts/eval_furniture.yml -i
```
### Train a model

Train furniture, using HRNet + OCR + multi-scale attention with fine data and mapillary-pretrained model
```bash
> python -m runx.runx scripts/train_furniture.yml -i
```
The first time this command is run, a centroid file has to be built for the dataset. It'll take about 10 minutes. The centroid file is used during training to know how to sample from the dataset in a class-uniform way.

For further information on how to use/train the network please see [nvidia-semantic-segmentation](https://github.com/NVIDIA/semantic-segmentation)

### Train model using augmenation with imgaug (desion implementation)
Example of command:
 CUDA_VISIBLE_DVICES=0 nohup python train_new.py --dataset nips --cv 0 --scale_max 1.0 --class_uniform_pct 1 --class_uniform_tile 128 --bs_trn 4 --bs_val 1 --poly_exp 2 --lr 5e-3 --max_epoch 5000 --dump_assets --n_scales 1.0 --supervised_mscale_loss_wt 0.05 --arch ocrnet.HRNet_Mscale --resume logs/outputs/train_nips/2024-06-07_train-nips_3/best_checkpoint_ep3283.pth --result_dir outputs/train_nips/2024-06-13_train-nips_aug --image_size [500,944] --augmentations [fliplr,flipud,affine,sharpen,colortemperature,randomsizeandcrop] > 2024-06-13_nips-13k.log &









