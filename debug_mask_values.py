#!/usr/bin/env python3
"""
Script to debug mask values in the medical dataset
"""
import os
import numpy as np
from PIL import Image
import glob
from collections import Counter

def check_mask_values(mask_dir):
    """Check unique values in all mask files"""
    print(f"Checking masks in: {mask_dir}")
    
    if not os.path.exists(mask_dir):
        print(f"Directory does not exist: {mask_dir}")
        return
    
    # Find all PNG files recursively
    mask_files = glob.glob(os.path.join(mask_dir, "**", "*.png"), recursive=True)
    print(f"Found {len(mask_files)} mask files")
    
    if len(mask_files) == 0:
        print("No mask files found!")
        return
    
    all_values = Counter()
    
    for i, mask_file in enumerate(mask_files[:10]):  # Check first 10 files
        try:
            mask = np.array(Image.open(mask_file))
            unique_values = np.unique(mask)
            print(f"File {i+1}: {os.path.basename(mask_file)}")
            print(f"  Shape: {mask.shape}")
            print(f"  Unique values: {unique_values}")
            print(f"  Min: {mask.min()}, Max: {mask.max()}")
            
            for val in unique_values:
                all_values[val] += np.sum(mask == val)
                
        except Exception as e:
            print(f"Error reading {mask_file}: {e}")
    
    print("\nSummary of all values found:")
    for val, count in sorted(all_values.items()):
        print(f"  Value {val}: {count} pixels")
    
    max_value = max(all_values.keys()) if all_values else 0
    print(f"\nMaximum value found: {max_value}")

    # Check the actual number of classes from the dataset loader
    try:
        from datasets.defis_medical import Loader
        num_classes = Loader.num_classes
        print(f"Dataset loader configured for: {num_classes} classes (0-{num_classes-1})")

        if max_value >= num_classes:
            print(f"⚠️  PROBLEM: Found values >= {num_classes}, but model expects only 0-{num_classes-1}!")
            print("This will cause CUDA device-side assert errors.")
        else:
            print(f"✅ OK: All values are within expected range (0-{num_classes-1})")
    except Exception as e:
        print(f"Could not check dataset loader: {e}")

if __name__ == "__main__":
    # Check the dataset directory from config
    from config import cfg
    
    dataset_dir = cfg.DATASET.DEFIS_MEDICAL_DIR
    print(f"Dataset directory from config: {dataset_dir}")
    
    # Check train and val masks
    for split in ['train', 'val']:
        mask_dir = os.path.join(dataset_dir, split, 'seg')
        print(f"\n{'='*50}")
        print(f"Checking {split} masks")
        print(f"{'='*50}")
        check_mask_values(mask_dir)
