# loss/focal.py
import torch
import torch.nn as nn
import torch.nn.functional as F

class FocalLoss(nn.Module):
    """
    Multi-class Focal loss with optional class weights.
    γ: focusing parameter
    α: balance parameter (use your class_weights tensor here)
    """
    def __init__(self, gamma=2.0, weight=None, ignore_index=255):
        super().__init__()
        self.gamma = gamma
        self.weight = weight
        self.ignore_index = ignore_index

    def forward(self, logits, targets, do_rmi=None, **_ignored):
        """
        logits : (N,C,H,W) raw scores from the net
        targets: (N,H,W)    ground-truth ids
        do_rmi : ignored – keeps the signature compatible with
                 RMILoss & the OCRNet helper.
        """
        logp = F.log_softmax(logits, dim=1)
        p    = torch.exp(logp)
        loss = F.nll_loss((1 - p) ** self.gamma * logp,
                          targets,
                          weight=self.weight,
                          ignore_index=self.ignore_index)
        return loss
