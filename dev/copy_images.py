import os
import shutil

# Define paths
png_folder = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_14/seg"
jpg_json_folder = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_14/all"
output_folder = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_14/images"

# Ensure output folder exists
os.makedirs(output_folder, exist_ok=True)

# Get list of PNG file names (without extension)
png_files = {os.path.splitext(f)[0] for f in os.listdir(png_folder) if f.endswith('.png')}

# Iterate over JPG files in the second folder
for file in os.listdir(jpg_json_folder):
    if file.endswith('.jpg'):
        jpg_name = os.path.splitext(file)[0]
        if jpg_name in png_files:
            # Copy matching JPG file to the output folder
            shutil.copy(os.path.join(jpg_json_folder, file), os.path.join(output_folder, file))

print("Copy completed.")
