import imgaug.augmenters as iaa
import random

import numpy as np
import torch



class RandomCorruption:
    def __init__(self, cfg):
        self.augmentations = cfg.AUGMENTATIONS
        self.min_augmentations = cfg.MIN_AUGMENTATIONS
        self.max_augmentations = cfg.MAX_AUGMENTATIONS

    def get_transform(self, img, mask):
        color_augmentation_list = []
        shape_augmentation_list = []
        do = random.random() > 0.5

        if do:
            if "fliplr" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(0.6, iaa.Fliplr(0.5))]
            if "flipud" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(0.6, iaa.Flipud(0.2))]
            if "affine" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(0.5, iaa.Affine(
                    scale={'x': (0.9, 1.1), 'y': (0.9, 1.1)},
                    shear=(-16, 16),
                    order=[0, 1],
                    mode="constant",
                    cval=0
                ))]
            if "randomsizeandcrop" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(
                    0.5,
                    iaa.CropAndPad(
                        percent=(-0.5, -0.2),  # Only crop by up to 20% of the image size, no padding
                        keep_size=True      # Keep the final image size the same as the original
                    )
                )]
            if "blur" in self.augmentations:
                color_augmentation_list += [iaa.Sometimes(
                    0.5,
                    iaa.OneOf([
                        iaa.GaussianBlur((0, 3)),
                        iaa.AverageBlur(k=(2, 7)),
                        iaa.MedianBlur(k=(3, 7)),
                        iaa.MotionBlur(k=(3, 7), angle=(-180, 180))
                    ])
                )]
            if "sharpen" in self.augmentations:
                color_augmentation_list += [iaa.Sometimes(
                    0.5,
                    iaa.Sharpen(alpha=(0, 0.3), lightness=(0.75, 1.5))
                )]
            if "huesaturation" in self.augmentations:
                color_augmentation_list += [iaa.Sometimes(
                    0.4,
                    iaa.OneOf([
                        iaa.AddToHueAndSaturation((-20, -20)),
                        iaa.AddToHueAndSaturation((20, 20))])
                )]
            if "colortemperature" in self.augmentations:
                color_augmentation_list += [iaa.Sometimes(
                    0.5,
                    iaa.ChangeColorTemperature((1000, 10000))
                )]
            if "rotate" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(
                    0.6,
                    [
                        iaa.Rotate((-5, 5), cval=0),
                    ])]
            if "cutout" in self.augmentations:
                shape_augmentation_list += [iaa.Sometimes(
                    0.25,
                    [
                        iaa.Cutout(nb_iterations=(1, 5), size=0.1, squared=False, cval=0)
                    ])]
            if "grayscale" in self.augmentations:
                color_augmentation_list += [iaa.Sometimes(
                    1,
                    iaa.Grayscale(alpha=(0.0, 1.0))
                )]

            shape_transform_list = iaa.Sequential(shape_augmentation_list)
            color_transform_list = iaa.Sequential(color_augmentation_list)

            shape_seq_det = shape_transform_list.to_deterministic()
            color_seq_det = color_transform_list.to_deterministic()

            augmented_image_shape = shape_seq_det.augment_images([img])[0]
            augmented_image = color_seq_det.augment_images([augmented_image_shape])[0]

            # Ensure gts has 3 channels before augmentation
            gts_expanded = np.expand_dims(mask, axis=-1)
            gts_expanded = np.concatenate([gts_expanded] * 3, axis=-1)

            augmented_gts = shape_seq_det.augment_images([gts_expanded.astype(np.uint8)])[0]
            augmented_gts = augmented_gts[:, :, 0]

            # transform_list = iaa.Sequential(augmentation_list)
            # seq_det = transform_list.to_deterministic()
            # #augmented_image = seq_det.augment_images([img.transpose(1, 2, 0).astype(np.uint8)])[0].transpose(2, 0, 1)
            # augmented_image = seq_det.augment_images([img])[0]
            #
            # # Ensure gts has 3 channels before augmentation
            # gts_expanded = np.expand_dims(mask, axis=-1)
            # gts_expanded = np.concatenate([gts_expanded] * 3, axis=-1)
            #
            # augmented_gts = seq_det.augment_images([gts_expanded.astype(np.uint8)])[0]
            #
            # # Convert augmented_gts back to its original shape if necessary
            # augmented_gts = augmented_gts[:, :, 0]
            # #augmented_gts = seq_det.augment_images([gts.astype(np.uint8)])[0]
        else:
            augmented_image = img
            augmented_gts = mask

        return augmented_image, augmented_gts