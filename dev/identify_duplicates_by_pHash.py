import os
import logging
from PIL import Image
import imagehash
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed

LOG_FILE = "/mnt/datasets/nips/database/duplicate_detector.log"

# Configure logging to write to a file
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s"
)

def compute_phash(file_path):
    """
    Compute the perceptual hash (pHash) of an image file.
    Returns a tuple of (pHash as a string, file_path).
    """
    try:
        with Image.open(file_path) as img:
            phash = imagehash.phash(img)
        return str(phash), file_path
    except Exception as e:
        logging.error(f"Error processing {file_path}: {e}")
        return None, file_path

def main():
    # Define the root directory and valid image extensions
    root_dir = "/mnt/data/datasets/nips/database"
    extensions = {".jpg", ".jpeg", ".png"}
    
    # Gather all image file paths
    image_files = []
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            ext = os.path.splitext(filename)[1].lower()
            if ext in extensions:
                image_files.append(os.path.join(dirpath, filename))
    
    total_images = len(image_files)
    logging.info(f"Total images to process: {total_images}")
    
    # Dictionary to store perceptual hashes and associated file paths
    hashes = defaultdict(list)
    
    # Use ProcessPoolExecutor to process images concurrently
    with ProcessPoolExecutor() as executor:
        # Submit tasks for each image file
        futures = {executor.submit(compute_phash, file_path): file_path for file_path in image_files}
        
        # Process results as they complete
        for idx, future in enumerate(as_completed(futures), 1):
            phash, file_path = future.result()
            if phash is not None:
                hashes[phash].append(file_path)
            
            # Log progress every 100 images
            if idx % 100 == 0:
                logging.info(f"Processed {idx}/{total_images}")
    
    # Log groups of potential duplicates (images with the same pHash)
    logging.info("Potential duplicates by perceptual hash:")
    for phash, files in hashes.items():
        if len(files) > 1:
            logging.info(f"\nHash {phash} found in {len(files)} files:")
            for f in files:
                logging.info("  " + f)

if __name__ == "__main__":
    main()
