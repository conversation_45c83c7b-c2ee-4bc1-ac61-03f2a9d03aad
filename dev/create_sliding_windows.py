import os
import json
import argparse
import random
import cv2

# Shapely 2.0+:
from shapely.geometry import Polygon
from shapely.validation import make_valid


def parse_args():
    parser = argparse.ArgumentParser(description="Sliding window on images and annotations, using Shapely 2.0+.")
    parser.add_argument("--input_folder", type=str, required=True,
                        help="Path to the folder containing images + annotation JSON files.")
    parser.add_argument("--output_folder", type=str, required=True,
                        help="Path to the folder where cropped images + JSON will be saved.")
    parser.add_argument("--window_size", type=int, default=1024,
                        help="Width/Height (in pixels) of the sliding window.")
    parser.add_argument("--overlap", type=int, default=200,
                        help="Overlap (in pixels) between consecutive windows.")
    parser.add_argument("--bg_percentage", type=float, default=0.1,
                        help="Fraction of background windows to keep [0.0 - 1.0].")
    return parser.parse_args()


def sliding_windows(img_width, img_height, window_size, overlap):
    """
    Generator that yields (x, y, w, h) for each sliding window over the image.
    step = window_size - overlap
    """
    step = window_size - overlap
    for y in range(0, img_height, step):
        for x in range(0, img_width, step):
            crop_w = min(window_size, img_width - x)
            crop_h = min(window_size, img_height - y)

            # Optional: skip partial windows if they're smaller than window_size
            # near the image's right/bottom edge.
            if crop_w < window_size and x + window_size <= img_width:
                continue
            if crop_h < window_size and y + window_size <= img_height:
                continue

            yield (x, y, crop_w, crop_h)


def load_annotation(json_path):
    with open(json_path, 'r') as f:
        data = json.load(f)
    return data


def save_annotation(data, save_path):
    with open(save_path, 'w') as f:
        json.dump(data, f, indent=4)


def crop_image(img, x, y, w, h):
    return img[y:y+h, x:x+w]


def polygon_in_window(shape, win_x, win_y, win_w, win_h):
    """
    Clips a LabelMe polygon shape against the window bounding box,
    returning a shape in local tile coordinates, or None if empty/non-polygon.
    Uses Shapely 2.0+ make_valid to fix invalid polygons.
    """
    original_points = shape["points"]

    # Skip if fewer than 3 distinct points; can't form a valid polygon
    if len(set(tuple(p) for p in original_points)) < 3:
        return None

    poly_global = Polygon(original_points)
    if not poly_global.is_valid:
        poly_global = make_valid(poly_global)
        if not poly_global.is_valid or poly_global.is_empty:
            return None

    # Window bounding box as a polygon
    window_poly_global = Polygon([
        (win_x,         win_y),
        (win_x + win_w, win_y),
        (win_x + win_w, win_y + win_h),
        (win_x,         win_y + win_h)
    ])

    intersection = poly_global.intersection(window_poly_global)
    if intersection.is_empty:
        return None

    # Handle possible MultiPolygon
    if intersection.geom_type == 'Polygon':
        clipped_coords = list(intersection.exterior.coords)
    elif intersection.geom_type == 'MultiPolygon':
        # Pick the largest polygon by area
        largest = max(intersection.geoms, key=lambda g: g.area)
        if largest.is_empty:
            return None
        clipped_coords = list(largest.exterior.coords)
    else:
        return None

    # Shapely polygons repeat the first coordinate at the end;
    # require at least 4 points to ensure 3 unique corners
    if len(clipped_coords) < 4:
        return None

    # Convert intersection coords to local tile coordinates (0,0 at top-left)
    local_points = [[pt[0] - win_x, pt[1] - win_y] for pt in clipped_coords]

    # Construct the new shape dictionary in LabelMe format
    new_shape = {
        "label": shape["label"],
        "points": local_points,
        "group_id": shape.get("group_id", None),
        "shape_type": shape["shape_type"],
        "flags": shape.get("flags", {})
    }
    return new_shape


def main():
    args = parse_args()

    input_folder = args.input_folder
    output_folder = args.output_folder
    window_size = args.window_size
    overlap = args.overlap
    bg_percentage = args.bg_percentage

    os.makedirs(output_folder, exist_ok=True)

    # Gather images (adjust extensions if needed)
    image_files = [f for f in os.listdir(input_folder)
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    for img_file in image_files:
        img_path = os.path.join(input_folder, img_file)
        base_name, _ = os.path.splitext(img_file)
        json_path = os.path.join(input_folder, base_name + ".json")

        if not os.path.exists(json_path):
            # If there's no matching JSON, skip or handle as empty annotation
            continue

        img = cv2.imread(img_path)
        if img is None:
            print(f"Warning: Could not read {img_path}. Skipping.")
            continue
        h, w, _ = img.shape

        # Load annotation
        annotation = load_annotation(json_path)
        shapes = annotation.get("shapes", [])

        # Slide over the image
        for idx, (x, y, crop_w, crop_h) in enumerate(
            sliding_windows(w, h, window_size, overlap)
        ):
            tile = crop_image(img, x, y, crop_w, crop_h)

            # Clip polygons to this window
            new_shapes = []
            for shape in shapes:
                if shape["shape_type"] == "polygon":
                    clipped = polygon_in_window(shape, x, y, crop_w, crop_h)
                    if clipped and len(clipped["points"]) >= 3:
                        new_shapes.append(clipped)
                # Handle other shape types if needed

            # Determine if background (no shapes)
            if len(new_shapes) == 0:
                # Keep only a fraction of background crops
                if random.random() > bg_percentage:
                    continue

            # Save the cropped tile
            out_image_name = f"{base_name}_{idx:04d}.jpg"
            out_image_path = os.path.join(output_folder, out_image_name)
            cv2.imwrite(out_image_path, tile)

            # Create and save the updated annotation
            new_annotation = {
                "version": annotation.get("version", "5.0.1"),
                "flags": annotation.get("flags", {}),
                "shapes": new_shapes,
                "imagePath": out_image_name,
                "imageData": None,
                "imageHeight": crop_h,
                "imageWidth": crop_w
            }

            out_json_name = f"{base_name}_{idx:04d}.json"
            out_json_path = os.path.join(output_folder, out_json_name)
            save_annotation(new_annotation, out_json_path)

    print("Sliding window processing complete.")


if __name__ == "__main__":
    main()
