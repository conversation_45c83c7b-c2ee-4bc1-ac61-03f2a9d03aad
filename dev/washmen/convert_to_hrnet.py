#!/usr/bin/env python3
import os
import json
import shutil
from glob import glob
from pathlib import Path
from tqdm import tqdm

# Directories
STAIN_DIR = "/mnt/datasets/washmen/converted_labelme"
DAMAGE_DIR = "/mnt/datasets/washmen/converted_labelme_damages"
OUTPUT_DIR = "/mnt/datasets/washmen/converted_for_training"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Label mappings
STAIN_CLASSES = {
    "DRY_STAIN", "WET_SIDE_STAIN", "PERMANENT_STAIN",
    "Blood Stain", "Curry", "Drink Stain", "Dye Stain", "Food Stain",
    "Ink Stain", "Lipstick", "Make up", "Mildew", "Mold", "Oil Stain",
    "Paint Stain", "Perfume", "Rust Stain", "Soil Stain", "Super Glue",
    "Sweat Stain", "Tar Stain", "Wax Stain", "Wine Stain", "others", "sweat"
}

DAMAGE_MAPPING = {
    "COLOR_RELATED_DAMAGE": "stain",
    "FABRIC_AND_MATERIAL_DAMAGE": "hole",
    "STRUCTURAL_DAMAGE": "fiber",
    "BUTTON_AND_EMBELLISHMENT_ISSUES": "button_missing"
}

def convert_file(json_path, output_dir):
    with open(json_path, "r") as f:
        data = json.load(f)

    new_shapes = []
    for shape in data.get("shapes", []):
        label = shape["label"]
        if label in STAIN_CLASSES:
            shape["label"] = "stain"
            new_shapes.append(shape)
        elif label in DAMAGE_MAPPING:
            shape["label"] = DAMAGE_MAPPING[label]
            new_shapes.append(shape)

    if not new_shapes:
        return  # No usable shapes

    data["shapes"] = new_shapes

    # Copy image
    image_src = os.path.join(os.path.dirname(json_path), data["imagePath"])
    image_dst = os.path.join(output_dir, data["imagePath"])
    if not os.path.exists(image_dst) and os.path.exists(image_src):
        shutil.copy2(image_src, image_dst)

    # Save updated JSON
    out_json = os.path.join(output_dir, Path(json_path).name)
    with open(out_json, "w") as f:
        json.dump(data, f, indent=2)

def process_all(input_dir):
    for json_file in tqdm(glob(os.path.join(input_dir, "*.json")), desc=f"Processing {input_dir}"):
        convert_file(json_file, OUTPUT_DIR)

process_all(STAIN_DIR)
process_all(DAMAGE_DIR)
