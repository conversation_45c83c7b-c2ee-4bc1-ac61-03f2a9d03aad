#!/usr/bin/env python3
import os
import json
import shutil
from glob import glob
from pathlib import Path
from PIL import Image

def yolo_to_labelme(txt_path, img_path, class_names):
    with Image.open(img_path) as im:
        w, h = im.size

    annotations = []
    with open(txt_path, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) != 5:
                continue
            cls_id, x_center, y_center, width, height = map(float, parts)
            cls_name = class_names[int(cls_id)]

            # convert to box corners
            x1 = (x_center - width / 2) * w
            y1 = (y_center - height / 2) * h
            x2 = (x_center + width / 2) * w
            y2 = (y_center + height / 2) * h

            shape = {
                "label": cls_name,
                "points": [[x1, y1], [x2, y2]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
            annotations.append(shape)

    labelme_json = {
        "version": "4.5.7",
        "flags": {},
        "shapes": annotations,
        "imagePath": os.path.basename(img_path),
        "imageData": None,
        "imageHeight": h,
        "imageWidth": w
    }

    return labelme_json

def collect_and_convert(root_dir, output_dir):
    root = Path(root_dir)
    output = Path(output_dir)
    output.mkdir(parents=True, exist_ok=True)

    for day_folder in root.glob("*"):
        if not day_folder.is_dir():
            continue

        image_folder = day_folder / "images"
        label_folder = day_folder / "labels"
        classes_txt = label_folder / "classes.txt"
        if not classes_txt.exists():
            continue

        with open(classes_txt, 'r') as f:
            class_names = [line.strip() for line in f.readlines()]

        for txt_file in label_folder.glob("*.txt"):
            if txt_file.name == "classes.txt":
                continue
            img_file = image_folder / (txt_file.stem + ".jpg")
            if not img_file.exists():
                continue

            labelme_data = yolo_to_labelme(txt_file, img_file, class_names)

            # Write JSON and copy image
            out_img = output / img_file.name
            out_json = output / (img_file.stem + ".json")
            shutil.copy2(img_file, out_img)
            with open(out_json, 'w') as jf:
                json.dump(labelme_data, jf, indent=2)

if __name__ == "__main__":
    collect_and_convert(
        "/mnt/datasets/washmen/samples/Damages-20250612T073556Z-1-001/Damages/",
        "/mnt/datasets/washmen/converted_labelme_damages/"
    )
