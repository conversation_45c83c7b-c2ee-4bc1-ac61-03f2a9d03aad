"""
# File: Timer.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
#   - Tools for image processing.
# Changelog:
    - 24.09.2022: Creation date
    - 21.05.2024: Review toc(). Can write a message
"""

import time
from statistics import mean, median


class Timer:
    def __init__(self):
        self._recent_start_time = None
        self.elapsed_times = []

    def tic(self):
        """Starts the timer and returns the start time."""
        self._recent_start_time = time.perf_counter()
        return self._recent_start_time

    def toc(self, specific_start_time=None, display_stats=False, round=True, msg=""):
        """
        Stops the timer and prints the elapsed time in milliseconds, optionally displaying statistics.
        """
        elapsed_time = self._get_elapsed_time(specific_start_time)
        elapsed_time_str = f"{elapsed_time:.2f} ms" if round else f"{elapsed_time} ms"

        print(f"{msg}{elapsed_time_str}")

        if display_stats:
            self.display_statistics()

        return elapsed_time

    def delta(self, specific_start_time=None) -> float:
        """
        Stops the timer and returns the elapsed time in milliseconds.
        Can accept a specific start time.
        """
        return self._get_elapsed_time(specific_start_time)

    def _get_elapsed_time(self, specific_start_time=None) -> float:
        """
        Gets the elapsed time based on the provided start time or the most recent start time.

        :return: Elapsed time since the given start time or the most recent one in milliseconds.
        """
        start_time = specific_start_time or self._recent_start_time
        if start_time is None:
            raise ValueError("Timer has not been started. Call tic() to start it.")

        elapsed_time = (time.perf_counter() - start_time) * 1000
        self.elapsed_times.append(elapsed_time)
        return elapsed_time

    def display_statistics(self):
        """Displays the mean and median of the elapsed times."""
        if not self.elapsed_times:
            print("No elapsed times recorded.")
            return

        avg_time = mean(self.elapsed_times)
        median_time = median(self.elapsed_times)

        print(f"Mean elapsed time: {avg_time:.2f}ms")
        print(f"Median elapsed time: {median_time:.2f}ms")
