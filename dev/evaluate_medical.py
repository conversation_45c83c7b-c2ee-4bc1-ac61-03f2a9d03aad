import logging
import sys
import os
import threading
import gc
import json
import csv

from dev.data_converter import yaml_file_to_dict
from dev.gpu_processor import GPUProcessor
from dev.image_processing import read_images_batch
from dev.timer import Timer
from dev.detector_hr import DetectorHR as Detector

import cv2
import numpy as np
from shapely.geometry import Polygon

# Constant variables
CONFIGS_PATH = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_07"
INPUT_DIR = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_07"
OUTPUT_DIR = "/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_07/evaluation"
MODEL_DIR = "/mnt/data/projects/hrnet/outputs/train_defis-medical/2024-10-23_train-defis-medical/best_checkpoint_ep3916.pth"
GPU = 0
BUFFER = 0
DONE = []
_lock = threading.Lock()
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(name)-10.10s | %(levelname)-6.6s | %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger("Evaluation")


if __name__ == '__main__':
    # ____ SETTINGS _____
    logger.info("Loading class map from %s", CONFIGS_PATH)
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map.yaml"))
    model_path = MODEL_DIR
    model_args = {
        "scales": [0.5, 1.0, 1.5],
        "arch": "network.ocrnet.HRNet_Mscale"
    }

    # Build class index to name mapping
    class_index_to_name = {}
    for class_name, v in class_map.items():
        class_id = v[2]  # Extract the id from the third element of the list
        class_index_to_name[class_id] = class_name

    # Initialize defect_counts
    defect_counts = {}
    for defect_type in class_map.keys():
        defect_counts[defect_type] = {'N_total': 0, 'N_correct': 0}

    # Initialize location accuracy counts
    N_total_location = 0
    N_correct_location = 0

    # ____ MODEL ____
    logger.info("Initializing the model")
    extractor = Detector(class_map=class_map, model_path=model_path, model_args=model_args, gpu=GPU,
                         image_size=(1, 1), min_confidence=0.1)

    # ___ MAIN LOOP ___
    do_while = True
    batch_id = 0
    batch_size = 100
    counter = 0
    total_images_processed = 0
    t = Timer()
    logger.info("Starting evaluation process")
    while do_while:
        logger.info("Reading batch %d", batch_id)
        images, paths = read_images_batch(INPUT_DIR, batch_id=batch_id, batch_size=batch_size, load_images=True)

        if not images:
            logger.info("No more images to process. Ending evaluation.")
            break

        for image, path in zip(images, paths):

            # Read label file
            label_path = os.path.splitext(path)[0] + '.json'
            if not os.path.exists(label_path):
                logger.warning("Label file not found for image %s", path)
                continue
            with open(label_path, 'r') as f:
                label = json.load(f)

            # Extract ground truth defect types and masks
            ground_truth_defects = []
            for shape in label.get('shapes', []):
                defect_type = shape.get('label')
                points = shape.get('points', [])
                if defect_type and points:
                    # Convert points to a Polygon
                    gt_polygon = Polygon(points)
                    ground_truth_defects.append({'type': defect_type, 'polygon': gt_polygon})

            # Run detection
            det = extractor.detect([image], merge_garment=False, images_in_memory=False, store_visualizations=False)[0]

            # Extract predicted defect types and masks
            predicted_defects = det.get("classes", [])
            predicted_contours  = det.get("masks", [])
            predicted_polygons = []
            for contour in predicted_contours:
                if len(contour) >= 3:
                    # Flatten the contour array and create a polygon
                    contour_points = contour.reshape(-1, 2)
                    pred_polygon = Polygon(contour_points)
                    if pred_polygon.is_valid:
                        predicted_polygons.append(pred_polygon)

            # For each ground truth defect
            for gt_defect in ground_truth_defects:
                defect_type = gt_defect['type']
                gt_polygon = gt_defect['polygon']

                # Update counts for classification accuracy
                defect_counts[defect_type]['N_total'] += 1
                if defect_type in predicted_defects:
                    defect_counts[defect_type]['N_correct'] += 1

                # Update counts for location accuracy
                N_total_location += 1
                found = False
                for pred_polygon in predicted_polygons:
                    if gt_polygon.intersects(pred_polygon):
                        N_correct_location += 1
                        found = True
                        break  # No need to check other predicted polygons
                if not found:
                    logger.debug("No matching detection for ground truth defect in image %s", path)

            counter += 1
            total_images_processed += 1
            logger.info("Processed image %d: %s", total_images_processed, path)

        # Compute and log current accuracies after the batch
        logger.info("Current accuracies after batch %d:", batch_id)
        for defect_type, counts in defect_counts.items():
            N_total = counts['N_total']
            N_correct = counts['N_correct']
            if N_total > 0:
                accuracy = N_correct / N_total
                logger.info("Defect type '%s': Accuracy = %.4f (%d/%d)",
                            defect_type, accuracy, N_correct, N_total)
            else:
                logger.info("Defect type '%s': No samples yet.", defect_type)

        # Log location accuracy
        if N_total_location > 0:
            location_accuracy = N_correct_location / N_total_location
            logger.info("Current location accuracy: %.4f (%d/%d)", location_accuracy, N_correct_location, N_total_location)
        else:
            logger.info("No ground truth defects encountered yet for location accuracy.")

        if len(images) < batch_size:
            logger.info("Processed the last batch of images.")
            do_while = False

        batch_id += 1
        del images
        del paths
        gc.collect()

    # After processing all images, compute final accuracy per defect type
    logger.info("Final accuracy per defect type:")
    accuracy_table = []
    for defect_type, counts in defect_counts.items():
        N_total = counts['N_total']
        N_correct = counts['N_correct']
        if N_total > 0:
            accuracy = N_correct / N_total
        else:
            accuracy = None  # or set to 0.0
        accuracy_table.append(
            {'defect_type': defect_type, 'accuracy': accuracy, 'N_total': N_total, 'N_correct': N_correct})

        logger.info("Defect type '%s': Accuracy = %s, N_total = %d, N_correct = %d",
                    defect_type, accuracy, N_total, N_correct)

    # Compute final location accuracy
    if N_total_location > 0:
        location_accuracy = N_correct_location / N_total_location
    else:
        location_accuracy = None

    logger.info("Final location accuracy: %s (%d/%d)", location_accuracy, N_correct_location, N_total_location)

    # Save the accuracy table to a file in OUTPUT_DIR
    output_file = os.path.join(OUTPUT_DIR, 'accuracy_per_defect_type.csv')
    logger.info("Saving accuracy results to %s", output_file)
    with open(output_file, 'w', newline='') as csvfile:
        fieldnames = ['defect_type', 'accuracy', 'N_total', 'N_correct']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for row in accuracy_table:
            writer.writerow(row)

        # Add location accuracy to the CSV
        writer.writerow({})
        writer.writerow({'defect_type': 'location_accuracy', 'accuracy': location_accuracy, 'N_total': N_total_location,
                         'N_correct': N_correct_location})

    logger.info("Evaluation completed. Accuracy per defect type saved to: %s", output_file)

    logger.info("Press button to exit...")
