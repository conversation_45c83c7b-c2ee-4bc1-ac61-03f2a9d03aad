import logging
import sys
import os
import threading
import gc
import json
import cv2
import numpy as np

from dev.data_converter import yaml_file_to_dict
from dev.image_processing import read_images_batch
from dev.timer import Timer
from dev.detector_hr import DetectorHR as Detector

# Constant variables
CONFIGS_PATH = "/mnt/data/projects/hrnet/configs/nips/"
INPUT_DIR = "/mnt/datasets/nips/raw_images/"
MODEL_DIR = "/mnt/data/projects/hrnet/outputs/train_nips/2024-11-18_train-nips/best_checkpoint_ep110.pth"
GPU = 0
BUFFER = 0
DONE = []
SCALES = {"scales": [0.5, 1.0, 1.5]}
_lock = threading.Lock()

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(name)-8.8s | %(levelname)-6.6s | %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger("PRE")

def on_image_processed(img_id: str, request_id: str, detection: dict) -> None:
    DONE.append(img_id)

if __name__ == '__main__':
    # ____ SETTINGS _____
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map.yaml"))
    model_path = MODEL_DIR
    model_args = SCALES

    # ____ MODEL ____
    extractor = Detector(class_map, model_path, model_args, gpu=GPU)

    # ___ MAIN LOOP ___
    do_while = True
    batch_id = 0
    batch_size = 100
    counter = 0
    t = Timer()

    while do_while:
        images, paths = read_images_batch(INPUT_DIR, batch_id=batch_id, batch_size=batch_size, load_images=True)

        for image, path in zip(images, paths):
            det = extractor.detect([image], images_in_memory=False, store_visualizations=False)[0]
            det["image"] = image
            det["path"] = path

            # Generate annotation
            annotation = Detector.generate_annotation(det)
            output_json_path = os.path.splitext(path)[0] + ".json"  # Save in the same directory as the image
            
            with open(output_json_path, "w+") as writefile:
                json.dump(annotation, writefile)
            
            # Save mask in the same directory as input
            #mask_output_path = os.path.splitext(path)[0] + ".png"
            #if "mask" in det and det["mask"] is not None:
            #    mask = (det["mask"] * 255).astype(np.uint8)  # Convert mask to 8-bit image
            #    cv2.imwrite(mask_output_path, mask)
            
            counter += 1
            print(f"{counter}: {path} -> {output_json_path}")

        if len(images) + 1 < batch_size:
            print("END BATCH@:")
            do_while = False

        batch_id += 1
        del images
        del paths
        gc.collect()

print("Press button to exit...")
