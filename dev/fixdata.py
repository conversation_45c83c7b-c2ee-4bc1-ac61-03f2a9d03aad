import os
import json

input_folder = '/mnt/data/datasets/defis/anomaly-detection/2024_medical-stockings_od_14/all/'

def process_json_file(file_path):
    with open(file_path, 'r') as file:
        data = json.load(file)
    
    # Change all label fields to lowercase and set imageData to ""
    for shape in data.get('shapes', []):
        shape['label'] = shape['label'].lower()
    data['imageData'] = None

    # Overwrite the JSON file with the updated data
    with open(file_path, 'w') as file:
        json.dump(data, file, indent=2)

def process_all_json_files(input_folder):
    for filename in os.listdir(input_folder):
        if filename.endswith('.json'):
            file_path = os.path.join(input_folder, filename)
            process_json_file(file_path)

# Example usage
process_all_json_files(input_folder)
