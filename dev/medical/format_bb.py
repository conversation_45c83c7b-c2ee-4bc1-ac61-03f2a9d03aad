import argparse
import json
import yaml
from pathlib import Path

CLASS_MAP = {
    "hole": 0,
    "strips": 1,
    "stain": 2,
    "line": 3,
    "knots": 4,
    "fiber": 5,
    "surface": 6,
    "elastic": 7,
    "pull": 8,
    "seam": 9,
    "band": 10,
    "pilling": 11,
    "knot": 12,
    "platine": 13,
    "colouring fold": 14
}
LABEL_ALIASES = {
    "strip": "strips",
    "strips": "strips",
    "pull": "pull",
    "pulls": "pull",
    "stain": "stain",
    "stains": "stain",
    "hole": "hole",
    "holes": "hole",
    "knot": "knots",
    "knots": "knots",

    "line": "line",
    "fiber": "fiber",
    "fibers": "fiber",
    "pilling": "pilling",
    "band": "band",
    "bands": "band",
    "seam": "seam",
    "seams": "seam",
    "platine": "platine",
    "coloring fold": "coloring_fold",
    "coloringfold": "coloring_fold",
    "pulling": "pull", 
}



def load_classmap_yaml(path):
    with open(path, 'r') as f:
        classmap_raw = yaml.safe_load(f)

    # Assign class IDs in order of appearance, skipping __background__
    label_to_id = {}
    current_id = 0
    for label in classmap_raw:
        if label == "__background__":
            continue
        label_to_id[label] = current_id
        current_id += 1
    return label_to_id

def polygon_to_bbox(points):
    x_coords = [pt[0] for pt in points]
    y_coords = [pt[1] for pt in points]
    return [
        int(min(x_coords)),
        int(min(y_coords)),
        int(max(x_coords)),
        int(max(y_coords)),
    ]

def convert_json(input_path, output_path, label_to_id):
    with open(input_path, "r") as f:
        data = json.load(f)

    image_filename = data["imagePath"]
    output_filename = Path(image_filename).name + "-copia.png"

    boxes = []
    labels = []

    for shape in data.get("shapes", []):
        label = shape['label'].strip().lower()
        label = LABEL_ALIASES.get(label, label)
        if label not in label_to_id:
            print(f"Skipping unknown label '{label}' in {input_path}")
            continue
        bbox = polygon_to_bbox(shape["points"])
        boxes.append(bbox)
        labels.append(label_to_id[label])

    result = {
        output_filename: {
            "boxes": boxes,
            "labels": labels
        }
    }

    output_file = output_path / (Path(input_path).stem + ".json")
    with open(output_file, "w") as f:
        json.dump(result, f, indent=4)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input_folder", type=str, help="Folder with LabelMe JSON files")
    parser.add_argument("output_folder", type=str, help="Folder to save converted JSON files")
    parser.add_argument("classmap", type=str, help="Path to classmap YAML file")
    args = parser.parse_args()

    input_folder = Path(args.input_folder)
    output_folder = Path(args.output_folder)
    output_folder.mkdir(parents=True, exist_ok=True)

    label_to_id = load_classmap_yaml(args.classmap)

    json_files = list(input_folder.glob("*.json"))
    print(f"Found {len(json_files)} JSON files.")

    for json_file in json_files:
        convert_json(json_file, output_folder, label_to_id)

if __name__ == "__main__":
    main()
