import os
import zipfile
from pathlib import Path

# Full path to the folder with images and labels
source_dir = Path("/mnt/data/datasets/defis/anomaly-detection/sigvaris/results_stk_5_detections_cvat")
output_dir = source_dir / "zipped_batches"
output_dir.mkdir(exist_ok=True)

# File extensions considered as images
image_extensions = ('.jpg', '.jpeg', '.png')

# List all image files
all_images = sorted([f for f in source_dir.iterdir() if f.suffix.lower() in image_extensions])

batch_size = 200

# Create zip files in batches
for i in range(0, len(all_images), batch_size):
    batch_images = all_images[i:i + batch_size]
    zip_filename = output_dir / f"batch_{i // batch_size + 1}.zip"

    with zipfile.ZipFile(zip_filename, 'w') as zipf:
        for img_path in batch_images:
            label_path = source_dir / f"{img_path.stem}.xml"

            if label_path.exists():
                zipf.write(img_path, arcname=img_path.name)
                zipf.write(label_path, arcname=label_path.name)
            else:
                print(f"Warning: No label found for {img_path.name}")
