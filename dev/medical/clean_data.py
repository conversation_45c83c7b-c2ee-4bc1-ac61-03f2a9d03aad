import json
import argparse
from pathlib import Path

# Define normalization rules (lowercase only)
NORMALIZATION_RULES = {
    # Canonical labels
    "strip": "strips",
    "strips": "strips",
    "pull": "pull",
    "pulls": "pull",
    "stain": "stain",
    "stains": "stain",
    "hole": "hole",
    "holes": "hole",
    "knot": "knots",
    "knots": "knots",

    # Additional variants observed in your dataset
    "line": "line",
    "fiber": "fiber",
    "fibers": "fiber",
    "pilling": "pilling",
    "band": "band",
    "bands": "band",
    "seam": "seam",
    "seams": "seam",
    "platine": "platine",
    "coloring fold": "coloring_fold",
    "coloringfold": "coloring_fold",
    "pulling": "pull",  # reasonable normalization
}

def normalize_label(label):
    return NORMALIZATION_RULES.get(label.lower())

def process_file(input_path: Path, output_path: Path):
    with open(input_path, 'r') as f:
        data = json.load(f)

    shapes = data.get("shapes", [])
    new_shapes = []
    modified = False

    for shape in shapes:
        original_label = shape["label"]
        normalized = normalize_label(original_label)
        if normalized:
            if normalized != original_label:
                print(f"Fixing label '{original_label}' → '{normalized}' in {input_path.name}")
                shape["label"] = normalized
                modified = True
            new_shapes.append(shape)
        else:
            print(f"Unknown label '{original_label}' in {input_path.name}, removing shape.")
            modified = True  # Shape is removed

    data["shapes"] = new_shapes

    # Write to output
    out_path = output_path / input_path.name
    with open(out_path, 'w') as f:
        json.dump(data, f, indent=2)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input_folder", type=str, help="Folder with original LabelMe JSONs")
    parser.add_argument("output_folder", type=str, help="Folder to save cleaned JSONs")
    args = parser.parse_args()

    input_dir = Path(args.input_folder)
    output_dir = Path(args.output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)

    json_files = list(input_dir.glob("*.json"))
    print(f"Found {len(json_files)} files.")

    for file in json_files:
        process_file(file, output_dir)

if __name__ == "__main__":
    main()
