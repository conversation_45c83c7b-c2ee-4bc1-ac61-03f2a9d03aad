from google.colab import auth
auth.authenticate_user()

from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
import io, os

folder_id = '1afUaKAewqdAa_E8uqJshzimdviCn0bh7'  # Your folder ID
save_dir = '/mnt/datasets/washmen/samples/'

os.makedirs(save_dir, exist_ok=True)

service = build('drive', 'v3')

def list_files(folder_id):
    query = f"'{folder_id}' in parents and trashed = false"
    return service.files().list(q=query, fields="files(id, name, mimeType)").execute()['files']

def download_file(file_id, name):
    request = service.files().get_media(fileId=file_id)
    fh = io.FileIO(os.path.join(save_dir, name), 'wb')
    downloader = MediaIoBaseDownload(fh, request)
    done = False
    while not done:
        status, done = downloader.next_chunk()

def process_folder(folder_id):
    files = list_files(folder_id)
    for f in files:
        if f['mimeType'] == 'application/vnd.google-apps.folder':
            subfolder = os.path.join(save_dir, f['name'])
            os.makedirs(subfolder, exist_ok=True)
            process_folder(f['id'])
        else:
            print(f"Downloading: {f['name']}")
            download_file(f['id'], f['name'])

process_folder(folder_id)
