import os
import json
import xml.etree.ElementTree as ET
import zipfile
import argparse
from glob import glob
import shutil
from datetime import datetime


def create_xml(labelme_data, output_dir):
    image_filename = labelme_data.get("imagePath", "unknown.jpg")
    root = ET.Element("annotation")

    ET.SubElement(root, "folder").text = ""
    ET.SubElement(root, "filename").text = image_filename

    source = ET.SubElement(root, "source")
    ET.SubElement(source, "sourceImage").text = "The MIT-CSAIL database of objects and scenes"
    ET.SubElement(source, "sourceAnnotation").text = "LabelMe Webtool"

    imagesize = ET.SubElement(root, "imagesize")
    ET.SubElement(imagesize, "nrows").text = str(labelme_data.get("imageHeight", 0))
    ET.SubElement(imagesize, "ncols").text = str(labelme_data.get("imageWidth", 0))

    valid_object_count = 0
    for i, shape in enumerate(labelme_data.get("shapes", [])):
        points = shape.get("points", [])
        if len(points) < 3:
            print(f"⚠️ Skipping shape in '{image_filename}' with < 3 points: {points}")
            continue

        obj = ET.SubElement(root, "object")
        ET.SubElement(obj, "name").text = shape.get("label", "unknown")
        ET.SubElement(obj, "id").text = str(i)
        ET.SubElement(obj, "deleted").text = "0"
        ET.SubElement(obj, "verified").text = "1"
        ET.SubElement(obj, "occluded").text = "no"
        ET.SubElement(obj, "date").text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        polygon = ET.SubElement(obj, "polygon")
        ET.SubElement(polygon, "username").text = "anonymous"
        for point in points:
            pt = ET.SubElement(polygon, "pt")
            ET.SubElement(pt, "x").text = str(int(point[0]))
            ET.SubElement(pt, "y").text = str(int(point[1]))

        valid_object_count += 1

    if valid_object_count == 0:
        return None

    xml_filename = os.path.join(output_dir, os.path.splitext(os.path.basename(image_filename))[0] + ".xml")
    tree = ET.ElementTree(root)
    tree.write(xml_filename, encoding="utf-8", xml_declaration=True)
    print(f"✅ XML generado: {xml_filename}")
    return xml_filename


def clear_output_folder(output_folder):
    if os.path.exists(output_folder):
        shutil.rmtree(output_folder)
    os.makedirs(output_folder)
    print(f"✅ Carpeta de salida '{output_folder}' limpia o creada.")


def convert_all_subfolders(input_root, output_root):
    clear_output_folder(output_root)
    subfolders = [f.path for f in os.scandir(input_root) if f.is_dir()]

    for subfolder in subfolders:
        all_jsons = glob(os.path.join(subfolder, '**', '*.json'), recursive=True)
        if not all_jsons:
            continue

        subfolder_name = os.path.basename(subfolder.rstrip('/'))
        output_folder = os.path.join(output_root, subfolder_name)
        os.makedirs(output_folder, exist_ok=True)

        for json_file in all_jsons:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    labelme_data = json.load(f)

                result = create_xml(labelme_data, output_folder)
                if not result:
                    print(f"❌ No valid shapes in {json_file}, skipping.")
                    continue

                image_filename = labelme_data.get("imagePath")
                if image_filename:
                    image_path = os.path.join(os.path.dirname(json_file), image_filename)
                    if os.path.exists(image_path):
                        shutil.copy(image_path, os.path.join(output_folder, os.path.basename(image_filename)))
            except Exception as e:
                print(f"❌ Error processing {json_file}: {e}")

        zip_path = os.path.join(output_root, f"{subfolder_name}.zip")
        try:
            with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(output_folder):
                    for file in files:
                        file_path = os.path.join(root, file)
                        zipf.write(file_path, os.path.relpath(file_path, output_folder))
        except Exception as e:
            print(f"❌ Error creating ZIP for {subfolder_name}: {e}")
        print(f"✅ ZIP generado: {zip_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Batch convert LabelMe JSON to CVAT XML and ZIP by subfolder.")
    parser.add_argument("--input", type=str, required=True, help="Root input folder with subfolders of LabelMe annotations")
    parser.add_argument("--output", type=str, required=True, help="Output folder for XMLs and ZIPs")
    args = parser.parse_args()

    convert_all_subfolders(args.input, args.output)
