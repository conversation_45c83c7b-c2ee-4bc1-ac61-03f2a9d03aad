"""
# File: gpu_processor.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
#          The GPUProcessor class handles GPU-based processing for images. It leverages multiple GPUs and threads to
#          efficiently process incoming image data, and ensures graceful cleanup of resources upon completion or
#          interruption.
# Changelog:
#    - 20.04.2020: Creation date
#    - 16.08.2023: Optimization. More descriptive constructor header
"""
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)

import atexit
import logging
import os.path
import uuid as uuid
import multiprocessing as mp

from events import Events
from image_processing import read_images
from timer import Timer

from datetime import datetime
from threading import Thread
from time import sleep, perf_counter


class GPUProcessor:

    def __init__(self, class_map, model_path, gpu_ids, n_threads, min_confidence, callback, store_vis=True,
                 dtype="hrnet", model_args={}):
        """
         The GPUProcessor class handles GPU-based processing for images. It leverages multiple GPUs and threads to
         efficiently process incoming image data, and ensures graceful cleanup of resources upon completion or 
         interruption.

        :param class_map: (dict) A mapping of classes.
        :param model_path: (str) Path to the model.
        :param gpu_ids: (list) List of GPU ids.
        :param n_threads: (int) Number of threads.
        :param min_confidence: (float) Minimum confidence score.
        :param store_vis: (bool) Whether to store visualizations or not. Default is True.
        :param callback: (callable) A callback function to execute upon an event.
        :param dtype: (str) Model framework to use: [hrnet, detectron, classnet].
        :param model_args: (dict) Model specific arguments
        """
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

        if dtype == "hrnet":
            from dev.detector_hr import DetectorHR as Detector
            self._Detector = Detector

            self.logger.info(f"Loading {dtype} framework.")
        else:
            raise (f"{dtype} is not a valid detector type")

        self._lock = mp.Lock()
        self._gpu_ids = gpu_ids
        self._n_threads = n_threads
        self._class_map = class_map
        self._model_path = model_path
        self._min_confidence = min_confidence
        self._store_vis = store_vis
        self._model_args = model_args

        self._dets_thread = None
        self._stop_dets_thread = False

        self._ctx = mp.get_context('spawn')
        self._qin = [self._ctx.Queue() for _ in gpu_ids]
        self._qout = self._ctx.Queue()
        self._events = Events()
        self._events.on("image_processed", callback=callback)
        self._processes = []

        #  Shared integer value initialized to 0 with child processed
        self._initialized_gpus = mp.Value('i', 0)
        self._requests_in_queue = mp.Value('i', 0)
        self._dets_completed = mp.Value('i', 0)

        # Next GPU/queue where the image must be sent
        self._next = 0
        self._t = Timer()

        atexit.register(self.stop)

        self.start()

    def _detect(self, imgid, request_id, image, model, qout):
        """
         Run evaluate on an image using the provided model.

         :param imgid: (str) Image ID.
         :param request_id: (str) Request ID.
         :param image: The image data.
         :param model: (Detector) Object detection extractor.
         :param qout: (Queue) Output queue.
         """

        self._t.tic()
        detections = model.detect([image], include_no_detections=True, images_in_memory=True,
                                  store_visualizations=self._store_vis)
        self.logger.debug(f"[{int(self._t.delta())} ms] Eval GPU-{model.gpu}. {imgid}.")

        qout.put((imgid, request_id, detections))

    def _run_gpu(self, gpu_id, n_threads, qin, qout):
        """
        Process the images using a GPU.

        :param gpu_id: (int) GPU ID to use for processing.
        :param n_threads: (int) Number of threads.
        :param qin: (Queue) Input queue.
        :param qout: (Queue) Output queue.
        """

        # Initialization
        model = self._Detector(self._class_map, self._model_path, min_confidence=self._min_confidence, gpu=gpu_id,
                               model_args=self._model_args)

        try:
            boost_folder = os.path.join(os.path.dirname(self._model_path), "boost")
            init_images, _ = read_images(boost_folder, load_images=True, filter="", max_img=80)

            start_time = perf_counter()
            # Run some images to avoid delay of first images.
            model.detect(init_images)
            self.logger.info(
                f"GPU {gpu_id} boost with {len(init_images)} images. {((perf_counter() - start_time) * 1000):.2f} ms")
        except Exception as ex:
            self.logger.warning(f"GPU {gpu_id} cannot be boosted: {boost_folder}. {str(ex)}")
            pass
        finally:
            with self._lock:
                self._initialized_gpus.value += 1

        # Maybe read some random images to increase the speed from first images to be processed
        list_threads = []

        running = True
        while running:
            # Process the images in batch or n_threads
            if len(list_threads) == n_threads:
                for tx in list_threads:
                    tx.join()

                list_threads = []

            if not qin.empty():
                with self._requests_in_queue.get_lock():
                    self._requests_in_queue.value -= 1

                (img_id, request_id, image) = qin.get()

                if img_id != "stop":
                    self.logger.debug(f"Analysing <{img_id}>")
                    thr = Thread(target=self._detect, args=(img_id, request_id, image, model, qout))
                    thr.start()
                    list_threads.append(thr)
                else:
                    self._stop_dets_thread = True
                    qout.put(("stop", None, None))
                    running = False

                    for tx in list_threads:
                        tx.join()
            else:
                sleep(0.001)

    def _trigger_dets(self):
        """
        Trigger image event as soon as an image in the output queue
        """

        while True:
            img_id, request_id, detections = self._qout.get()

            # Stop signal
            if img_id == "stop":
                break

            self._events.trigger("image_processed", img_id, request_id, detections)

            with self._dets_completed.get_lock():
                self._dets_completed.value -= 1

    def start(self):
        """
        Initialized GPU processor. One process per GPU
        """
        self._processes = [
            mp.Process(target=self._run_gpu, args=(int(gpu_id), self._n_threads, q, self._qout))
            for gpu_id, q in zip(self._gpu_ids, self._qin)
        ]
        for pr in self._processes:
            pr.start()

        self._dets_thread = Thread(target=self._trigger_dets)
        self._dets_thread.start()

    def stop(self):
        """
        Send the signal to stops all the process
        """

        self.is_initialized()
        sleep(1)

        # Send signal to stop all process and threads
        for qin in self._qin:
            qin.put(("stop", 0, 0))

        # Join()
        self.wait_until_all_processed()
        self._dets_thread.join()

    def put(self, image, img_id=None, request_id=None) -> str:
        """
        GPU load balancer. Put in the queue an image to be analyzed
        :param image: RBG image to be analyzed
        :param img_id: [Optional] Id of the image.
        :param request_id: [Optional] Id of the current request.
        :return: the unique id of the image generated
        """
        if img_id is None:
            img_id = "img_%s_%s" % (str(uuid.uuid4())[-4:], str(datetime.now()))

        if request_id is None:
            request_id = "req_%s_%s" % (str(uuid.uuid4())[-4:], str(datetime.now()))

        with self._requests_in_queue.get_lock():
            self._requests_in_queue.value += 1
        with self._dets_completed.get_lock():
            self._dets_completed.value += 1

        self._qin[self._next].put((img_id, request_id, image))

        self._next += 1
        if self._next >= len(self._qin):
            self._next = 0

        return img_id

    def is_initialized(self, timeout=30) -> bool:
        """
        Check if all GPUs are initialized within the given timeout period.

        This function continuously checks whether the number of GPUs that have been
        initialized matches the total number of GPUs. It returns True once all GPUs
        are initialized or if the timeout period is reached without initialization.

        :param timeout: The maximum time (in seconds) to wait for all GPUs to be initialized. Default is 30 seconds.
        :return: True if all GPUs are initialized within the timeout period, False otherwise.
        """
        start = perf_counter()
        while perf_counter() - start < timeout:
            if self._initialized_gpus.value == len(self._gpu_ids):
                return True
            sleep(0.1)

        return False

    def wait_until_all_processed(self, timeout=30):
        """
        Wait until all the requests are processed and all detections have been triggered.

        :param timeout: (int) Maximum number of seconds to wait.
        :return: (bool) True if all tasks are completed before the timeout, otherwise False.
        """
        start_time = perf_counter()

        while self._requests_in_queue.value > 0 or self._dets_completed.value > 0:
            if perf_counter() - start_time > timeout:
                return False
            sleep(0.1)  # Sleep for 100ms and check again

        return True


# @profile
if __name__ == '__main__':
    global t
    t = Timer()
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')


    def on_image_processed(imgid, request_id, detections):
        global t
        print(f"{t.delta():.2f} | On-image. {imgid} | {request_id} | {len(detections)}")


    # Load from disk all images
    imgs, paths = read_images("static/images/", 3, 8, load_images=True)
    class_map = json_to_dict("configs/class_map.json")

    # Load processors on each gpu

    p = GPUProcessor(class_map=class_map, model_path="models/20230803_ptp_mask.pth", gpu_ids=[1, 2], n_threads=1,
                     min_confidence=0.7, callback=on_image_processed)
    p.start()

    print(p.is_initialized())

    t.tic()
    req_id = 0
    for i in range(len(imgs)):
        print(f"Putting {i} | | {paths[i]}")
        p.put(imgs[i], paths[i], "r" + str(i))

    p.wait_until_all_processed()
    t.toc()
