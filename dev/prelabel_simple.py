import logging
import sys
import os

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, PROJECT_ROOT)

import threading
import gc
import json


from dev.data_converter import yaml_file_to_dict
# from dev.gpu_processor import GPUProcessor
from dev.image_processing import read_images_batch
from dev.timer import Timer
from dev.detector_hr import DetectorHR as Detector

# import cv2

# Constant variables
CONFIGS_PATH = "/mnt/data/projects/hrnet/configs/nips/"
INPUT_DIR = "/mnt/datasets/nips/simon/2024-07-08_nips_02"
OUTPUT_DIR="/mnt/datasets/nips/simon/2024-07-08_nips_02"
MODEL_DIR = "/mnt/data/projects/hrnet/outputs/train_nips/2024-11-18_train-nips/best_checkpoint_ep110.pth"
GPU=3
BUFFER = 0
DONE=[]
_lock = threading.Lock()
os.makedirs(OUTPUT_DIR,exist_ok=True)
logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s | %(name)-8.8s | %(levelname)-6.6s | %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
)

logger = logging.getLogger("PRE")



def on_image_processed(img_id: str, request_id: str, detection:dict) -> None:



    DONE.append(img_id)
    #threading.Thread(target=cv2.imwrite, args=(
    #                f"dev/static/{img_id}-vis.jpg", detection[0]['mask'], [int(cv2.IMWRITE_JPEG_QUALITY), 100])).start()




if __name__ == '__main__':
    # ____ SETTINGS _____
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map.yaml"))
    # model_path = "models/nips/best_checkpoint_ep1421.pth"
    # model_path = "models/nips/2024-05-21_nips_2957.pth"
    model_path = MODEL_DIR
    model_args ={
        "scales":[0.5]
    }

    # ____ MODEL ____
    extractor = Detector(class_map, model_path, model_args, gpu=GPU)

    # ___ MAIN LOOP ___
    do_while = True
    batch_id = 0
    batch_size = 100
    counter=0
    t = Timer()
    #os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    while do_while:
        images, paths = read_images_batch(INPUT_DIR, batch_id=batch_id, batch_size=batch_size, load_images=True)

        for image, path in zip(images, paths):
            det = extractor.detect([image], images_in_memory=False, store_visualizations=False)[0]
            det["image"] = image
            det["path"] = path
            #Detector.generate_annotation(os.path.basename(path[:-4])+".json", det["masks"], det["classes"], OUTPUT_DIR)
            annotation = Detector.generate_annotation(det)
            with open(os.path.join(OUTPUT_DIR, os.path.basename(path[:-4])+".json"), "w+") as writefile:
                json.dump(annotation, writefile)
            counter+=1
            print(f"{counter}: {path}")

        if len(images)+1 < batch_size:
            print("END BATCH@:")
            do_while = False

        batch_id += 1
        del images
        del paths
        gc.collect()




print("Press button to exit...")
