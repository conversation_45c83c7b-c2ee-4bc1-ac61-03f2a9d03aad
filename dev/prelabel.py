import logging
import sys
import os
import threading
import gc

from dev.data_converter import yaml_file_to_dict, dict_to_json_file
from dev.gpu_processor import GPUProcessor
from dev.image_processing import read_images_batch
from dev.timer import Timer
from dev.detector_hr import DetectorHR as Detector
import cv2

# Constant variables
CONFIGS_PATH = "dev"
OUTPUT_DIR = "/mnt/data/projects/nips/static/results/white-labels"
INPUT_DIR = "/mnt/data/projects/nips/static/results/white/"
BUFFER = 0
DONE = []
_lock = threading.Lock()

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(name)-8.8s | %(levelname)-6.6s | %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger("PRE")


def on_image_processed(img_id: str, request_id: str, detection: dict) -> None:
    # Detector.generate_annotation(f"{img_id}.jpg", detection[0]["masks"], detection[0]["classes"], OUTPUT_DIR)
    detection[0]["path"] = f"{img_id}.jpg"
    anno = Detector.generate_annotation(detection[0])
    dict_to_json_file(anno, os.path.join(OUTPUT_DIR, f"{img_id}.json"))

    DONE.append(img_id)
    threading.Thread(target=cv2.imwrite, args=(os.path.join(OUTPUT_DIR, detection[0]["path"]), detection[0]['image'],
                                               [int(cv2.IMWRITE_JPEG_QUALITY), 100])).start()
    threading.Thread(target=cv2.imwrite, args=(os.path.join(OUTPUT_DIR, f"{img_id}-msk.jpg"), detection[0]['mask'],
                                               [int(cv2.IMWRITE_JPEG_QUALITY), 100])).start()


if __name__ == '__main__':
    # ____ SETTINGS _____
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map-wsp.yaml"))
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map.yaml"))

    model_path = "models/wsp/20230116_wsp_999.pth"
    model_path = "models/nips/2024-07-16_nips_3335.pth"
    #model_path = "models/nips/2024-06-07_nips_a3281.pth"
    # model_path = "/mnt/data/projects/hrnet/outputs/train_nips/2024-07-03_train-nips_2/best_checkpoint_ep3302.pth"
    model_args = {
        "scales": [0.5]
    }

    # ____ MODEL ____
    processor = GPUProcessor(class_map=class_map, model_path=model_path, gpu_ids=[0,2,4], n_threads=1,
                             min_confidence=0.5, callback=on_image_processed, dtype="hrnet", model_args=model_args)
    processor.is_initialized(timeout=60)

    # ___ MAIN LOOP ___
    do_while = True
    batch_id = 0
    batch_size = 20
    t = Timer()
    while do_while:

        images = None
        paths = None

        images, paths = read_images_batch(INPUT_DIR, batch_id=batch_id, batch_size=batch_size, load_images=True,
                                          format="jpg")

        for image, path in zip(images, paths):
            name = os.path.basename(path[:-4])
            processor.put(image, os.path.basename(path[:-4]))
            print(name)

        processor.wait_until_all_processed(timeout=500)
        gc.collect()

        if len(images) + 1 < batch_size:
            print("END BATCH@:")
            do_while = False

        batch_id += 1

# print("Press button to exit...")
