#!/usr/bin/env python3
import os
import torch

# ─── CONFIG ───────────────────────────────────────────────────────────────────────

# Path to your original checkpoint
SRC_CKPT = '/mnt/data/projects/hrnet/outputs/train_nips/2025-05-02_MEWA_overfitted/best_checkpoint_ep80.pth'

# Directory where you want to write the wrapped, NumPy-1.x-compatible checkpoint
DST_DIR = '/mnt/data/projects/hrnet/models'

# Filename for the wrapped checkpoint
DST_FILENAME = 'MEWA_overfitted_np-1x.pth'

# ─── SCRIPT ───────────────────────────────────────────────────────────────────────

def main():
    os.makedirs(DST_DIR, exist_ok=True)
    dst_path = os.path.join(DST_DIR, DST_FILENAME)

    # 1) Load full checkpoint, allowing all pickled globals
    ckpt = torch.load(SRC_CKPT, map_location='cpu', weights_only=False)

    # 2) Extract the raw state_dict (or use ckpt itself if no 'state_dict' key)
    state = ckpt.get('state_dict', ckpt)

    # 3) Wrap under 'state_dict' so restore_net() will find it
    wrapped = {'state_dict': state}

    # 4) Save with legacy (pre-zip) serialization for maximum backward compatibility
    torch.save(wrapped, dst_path, _use_new_zipfile_serialization=False)

    print(f'Wrapped checkpoint written to: {dst_path}')

if __name__ == '__main__':
    main()
