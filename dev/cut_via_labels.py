import shutil
import json
from pathlib import Path

# Paths
BASE_DIR = Path("/mnt/datasets/nips/raw_images")
DEST_BASE_DIR = Path("/mnt/datasets/nips/CWS")

# Image extensions to check for
IMAGE_EXTENSIONS = [".jpg", ".jpeg", ".png"]

def move_file(src_path, dest_path):
    """Move a file while preserving directory structure."""
    dest_path.parent.mkdir(parents=True, exist_ok=True)  # Ensure the destination directory exists
    shutil.move(str(src_path), str(dest_path))  # Move the file

def process_json(json_path, search_labels):
    """Check if the JSON file contains specific labels."""
    try:
        with json_path.open("r", encoding="utf-8") as file:
            data = json.load(file)

        if isinstance(data, dict) and "shapes" in data:
            for shape in data["shapes"]:
                if shape.get("label") in search_labels:
                    return True  # Label found, move files
    except (json.JSONDecodeError, UnicodeDecodeError):
        return False
    return False

def move_files(json_path):
    """Move both the JSON file and its corresponding image."""
    relative_path = json_path.relative_to(BASE_DIR)  # Get relative path
    dest_dir = DEST_BASE_DIR / relative_path.parent  # Keep original structure

    # Move JSON file
    dest_json_path = dest_dir / json_path.name
    move_file(json_path, dest_json_path)

    # Find and move the corresponding image
    for ext in IMAGE_EXTENSIONS:
        image_path = json_path.with_suffix(ext)
        if image_path.exists():
            dest_image_path = dest_dir / image_path.name
            move_file(image_path, dest_image_path)
            break  # Move only the first valid image

def traverse_directory(search_labels):
    """Recursively scan the directory for JSON files and move them if needed."""
    total_jsons = 0
    moved_jsons = 0

    for json_path in BASE_DIR.rglob("*.json"):  # Recursively find all JSON files
        total_jsons += 1
        if process_json(json_path, search_labels):
            move_files(json_path)
            moved_jsons += 1

    print("\n✅ Scan Complete")
    print(f"🔍 Total JSON files checked: {total_jsons}")
    print(f"📂 JSON files moved: {moved_jsons}")

# Labels to search for
search_labels = ["polo", "t-shirt", "vest"]

# Run the script
traverse_directory(search_labels)
