import os
import json
import cv2
import numpy as np
from glob import glob
from pathlib import Path
from tqdm import tqdm

# Source directories
source_dirs = [
    "/home/<USER>/shared/projects/nips_cws/04_software/database/06_database/added_2024-06-12",
    "/home/<USER>/shared/projects/nips_cws/04_software/database/06_database/added_2024-06-28",
    "/home/<USER>/shared/projects/nips_cws/04_software/database/06_database/added_2024-07-09/train",
    "/home/<USER>/shared/projects/nips_cws/04_software/database/06_database/added_2024-07-09/test"
]

# Destination directory
destination_dir = "/mnt/datasets/nips/nips_rotated/all"
os.makedirs(destination_dir, exist_ok=True)

def rotate_image_and_annotation(image_path, annotation_path, dest_image_path, dest_annotation_path):
    try:
        # Read image
        image = cv2.imread(image_path)
        h, w = image.shape[:2]

        # Determine if rotation is needed (make the longer side the Y axis)
        if w > h:
            # Rotate image 90 degrees clockwise
            rotated_image = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
        else:
            rotated_image = image

        # Save rotated image
        cv2.imwrite(dest_image_path, rotated_image)

        # Update annotation file
        with open(annotation_path, 'r') as f:
            annotation = json.load(f)

        # Ensure keys exist before attempting to swap
        if 'imageWidth' in annotation and 'imageHeight' in annotation:
            if w > h:
                new_shapes = []
                for shape in annotation['shapes']:
                    new_points = [[p[1], w - p[0]] for p in shape['points']]
                    shape['points'] = new_points
                    new_shapes.append(shape)
                annotation['shapes'] = new_shapes
                annotation['imageHeight'], annotation['imageWidth'] = annotation['imageWidth'], annotation['imageHeight']

            annotation['imagePath'] = Path(dest_image_path).name

            # Save updated annotation
            with open(dest_annotation_path, 'w') as f:
                json.dump(annotation, f, indent=2)
        else:
            print(f"Skipping file due to missing keys: {annotation_path}")
    
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        # Skip this image and annotation

# Process each source directory
for source_dir in source_dirs:
    image_files = glob(os.path.join(source_dir, "*.jpg"))
    
    # Progress bar for the current directory
    for image_file in tqdm(image_files, desc=f"Processing {source_dir.split('/')[-1]}"):
        annotation_file = image_file.replace(".jpg", ".json")
        if os.path.exists(annotation_file):
            dest_image_file = os.path.join(destination_dir, Path(image_file).name)
            dest_annotation_file = os.path.join(destination_dir, Path(annotation_file).name)
            
            rotate_image_and_annotation(image_file, annotation_file, dest_image_file, dest_annotation_file)