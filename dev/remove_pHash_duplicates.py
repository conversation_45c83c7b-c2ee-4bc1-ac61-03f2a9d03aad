import os
import re
import math
import tkinter as tk
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.widgets import Button

LOG_FILE = "/mnt/datasets/nips/database/duplicate_detector.log"  # Adjust path if needed

def parse_log_file(log_file):
    """
    Parse the log file to extract groups of duplicate images.
    The log file is assumed to have groups in the following format:
    
    Hash <phash> found in <n> files:
    <timestamp> INFO:   /path/to/image1
    <timestamp> INFO:   /path/to/image2
    ...
    
    Returns a list of groups, where each group is a list of file paths.
    """
    groups = []
    current_group = []
    
    with open(log_file, "r") as f:
        for line in f:
            line = line.rstrip()
            # Detect group header lines starting with "Hash " and containing "found in"
            if line.startswith("Hash ") and "found in" in line:
                if current_group:
                    groups.append(current_group)
                    current_group = []
            else:
                # Look for lines that include "INFO:" and extract the file path.
                match = re.search(r"INFO:\s+(.*)", line)
                if match:
                    file_path = match.group(1).strip()
                    # Ensure that we have a valid file path (starts with /)
                    if file_path.startswith("/"):
                        current_group.append(file_path)
    if current_group:
        groups.append(current_group)
    return groups

def display_group(group):
    """
    Display images in a group arranged in a grid that fills available screen space.
    Clicking on an image deletes that image and its associated JSON file.
    Pressing "n" acts like clicking the "Next" button.
    Pressing "c" deletes every image/json pair except the first image in the group.
    """
    n = len(group)
    # Determine grid layout: use up to 4 columns per row.
    cols = min(n, 4)
    rows = math.ceil(n / cols)
    
    # Use Tkinter to get available screen dimensions (in inches)
    root_tk = tk.Tk()
    dpi = plt.rcParams['figure.dpi']
    screen_width_inches = root_tk.winfo_screenwidth() / dpi * 0.9  # use 90% of screen width
    screen_height_inches = root_tk.winfo_screenheight() / dpi * 0.9  # use 90% of screen height
    root_tk.destroy()
    
    # Choose figure size: full width and a height that depends on number of rows.
    fig_width = screen_width_inches
    fig_height = screen_height_inches * 0.6 if rows == 1 else screen_height_inches * 0.8
    
    fig, axes = plt.subplots(rows, cols, figsize=(fig_width, fig_height))
    
    # Flatten axes to a 1D list for easy iteration.
    if rows * cols == 1:
        axes = [axes]
    elif rows == 1 or cols == 1:
        axes = list(axes)
    else:
        axes = axes.flatten()
    # Hide any extra axes
    for ax in axes[n:]:
        ax.set_visible(False)
    axes = axes[:n]
    
    # Mapping from axis to file path
    ax_to_file = {}
    
    # Function to delete an image and its associated JSON for a given axis.
    def delete_axis(ax):
        if ax in ax_to_file:
            file_to_delete = ax_to_file[ax]
            # Delete the image file
            try:
                os.remove(file_to_delete)
                print(f"Deleted image: {file_to_delete}")
            except Exception as e:
                print(f"Error deleting image {file_to_delete}: {e}")
            # Delete the associated JSON file (if exists)
            json_file = os.path.splitext(file_to_delete)[0] + ".json"
            if os.path.exists(json_file):
                try:
                    os.remove(json_file)
                    print(f"Deleted JSON file: {json_file}")
                except Exception as e:
                    print(f"Error deleting JSON file {json_file}: {e}")
            else:
                print(f"JSON file not found for: {file_to_delete}")
            
            # Update subplot visually to mark deletion
            ax.clear()
            ax.set_title("DELETED", color="red", fontsize=8)
            ax.text(0.5, 0.5, "Deleted", horizontalalignment="center", verticalalignment="center",
                    transform=ax.transAxes, color="red", fontsize=12)
            fig.canvas.draw()
            # Remove axis from mapping so it won't be processed again
            del ax_to_file[ax]
    
    # Display each image in its subplot
    for ax, file_path in zip(axes, group):
        try:
            img = mpimg.imread(file_path)
            ax.imshow(img, picker=True)
            ax.axis("off")
            ax.set_title(os.path.basename(file_path), fontsize=8)
            ax_to_file[ax] = file_path
        except Exception as e:
            ax.text(0.5, 0.5, f"Error loading image:\n{e}",
                    horizontalalignment="center", verticalalignment="center")
            ax.axis("off")
    
    # Callback for mouse click on an image axis.
    def on_click(event):
        ax = event.inaxes
        if ax in ax_to_file:
            delete_axis(ax)
    
    cid_click = fig.canvas.mpl_connect("button_press_event", on_click)
    
    # Key press handler for "n" and "c"
    def on_key(event):
        # If "n" is pressed, close the figure (like clicking Next)
        if event.key.lower() == "n":
            plt.close(fig)
        # If "c" is pressed, delete every image/json pair except the first image.
        elif event.key.lower() == "c":
            # Assume the first image corresponds to axes[0]
            for ax in axes[1:]:
                if ax in ax_to_file:
                    delete_axis(ax)
    
    cid_key = fig.canvas.mpl_connect("key_press_event", on_key)
    
    # Add a "Next" button to allow moving to the next group.
    button_ax = fig.add_axes([0.45, 0.01, 0.1, 0.05])  # position at bottom center
    next_button = Button(button_ax, "Next")
    
    def on_next(event):
        plt.close(fig)
    
    next_button.on_clicked(on_next)
    
    # Adjust layout manually to ensure room for the button
    fig.subplots_adjust(bottom=0.15, wspace=0.1, hspace=0.1)
    plt.show()
    
    # Disconnect event handlers when done.
    fig.canvas.mpl_disconnect(cid_click)
    fig.canvas.mpl_disconnect(cid_key)

def main():
    groups = parse_log_file(LOG_FILE)
    print(f"Found {len(groups)} duplicate groups in the log file.")
    
    # Process each duplicate group with at least 2 images.
    for i, group in enumerate(groups, start=1):
        if len(group) < 2:
            continue
        print(f"\nDisplaying group {i} with {len(group)} images.")
        display_group(group)

if __name__ == "__main__":
    main()
