import os
import re
import json
import shutil
from collections import defaultdict, Counter

# ----------------------
# 1) Grouping logic
# ----------------------
class NameGrouper:
    FORBIDDEN = {"front", "back", "vis", "nir", "right", "left", "org"}

    @staticmethod
    def extract_item_id(filename):
        # Special Case 1
        m1 = re.search(r"(\d{2}-\d{2}-\d{2})_cws_clothes", filename)
        if m1:
            return m1.group(1)

        name, _ = os.path.splitext(filename)
        tokens = name.split('_')
        # Date-time prefixes
        if len(tokens) >= 3 and re.fullmatch(r"\d{4}-\d{2}-\d{2}", tokens[0]) \
                         and re.fullmatch(r"\d{2}-\d{2}-\d{2}", tokens[1]):
            return (f"{tokens[1]}_{tokens[2]}" if not tokens[2].isdigit()
                    else f"{tokens[2]}_{tokens[1]}")
        # Three leading numeric tokens
        if len(tokens) >= 3 and all(t.isdigit() for t in tokens[:3]):
            return f"{tokens[1]}_{tokens[2]}"

        # General extraction
        def forbidden(t): return any(f in t.lower() for f in NameGrouper.FORBIDDEN)
        filtered = [
            t for t in tokens
            if not re.fullmatch(r"\d{4}-\d{2}-\d{2}", t)
            and not re.fullmatch(r"\d{2}-\d{2}-\d{2}", t)
            and not forbidden(t)
        ]
        candidates = []
        for t in filtered:
            # dotted numeric parts
            if '.' in t:
                parts = t.split('.')[1:]
                candidates += [p for p in parts if p.isdigit()]
            # numeric prefix
            m = re.match(r"(\d+)", t)
            if m:
                candidates.append(m.group(1))
        if candidates:
            return max(candidates, key=len)
        if filtered:
            return max(filtered, key=len)
        return name

def gather_groups(root, splits=('train','val','test'), min_name_len=10, min_group_size=4):
    """
    Walk each split/images, collect (jpg, json) tuples, group by item_id,
    then return only those with >= min_group_size.
    """
    all_groups = defaultdict(list)
    for split in splits:
        img_dir = os.path.join(root, split, 'images')
        if not os.path.isdir(img_dir):
            continue
        # collect tuples
        tuples = []
        for fn in os.listdir(img_dir):
            if not fn.lower().endswith('.jpg'):
                continue
            base = os.path.splitext(fn)[0]
            if len(base) < min_name_len:
                continue
            jpg = os.path.join(img_dir, fn)
            jsn = os.path.join(img_dir, base + '.json')
            if os.path.isfile(jsn):
                tuples.append((jpg, jsn))
        # group
        groups = defaultdict(list)
        for jpg, jsn in tuples:
            iid = NameGrouper.extract_item_id(os.path.basename(jpg))
            groups[iid].append((jpg, jsn))
        # filter by size
        for iid, items in groups.items():
            if len(items) >= min_group_size:
                all_groups[iid].extend(items)

    # return a dict: item_id -> list of (jpg, json)
    return all_groups

# ----------------------
# 2) Splitting + priority + garment detection
# ----------------------
PRIORITY = {"40302906", "40302907", "40441310", "40451361"}
GARMENTS = {
    "pants", "jacket", "overall", "t-shirt", "vest",
    "polo", "apron", "shirt", "pullover"
}

def pick_first_four(pairs):
    """
    For the first set of 4:
      1) One per PRIORITY token, preferring 'vis' variant if duplicates.
      2) Then fill to 4 from the rest in sorted order.
    """
    selected = []
    used = set()  # basenames

    # 1) Unique priority tokens
    for tok in PRIORITY:
        # candidates matching this token
        cands = [p for p in pairs if tok in os.path.basename(p[0]) 
                 and os.path.basename(p[0]) not in used]
        if not cands:
            continue
        # prefer 'vis'
        vis = [p for p in cands if 'vis' in os.path.basename(p[0])]
        pick = vis[0] if vis else cands[0]
        selected.append(pick)
        used.add(os.path.basename(pick[0]))
        if len(selected) == 4:
            return selected

    # 2) fill to 4
    for p in pairs:
        b = os.path.basename(p[0])
        if b in used:
            continue
        selected.append(p)
        used.add(b)
        if len(selected) == 4:
            break

    return selected

def detect_garment(json_paths):
    cnt = Counter()
    for jsn in json_paths:
        with open(jsn, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for shape in data.get("shapes", []):
            lbl = shape.get("label", "").lower()
            if lbl in GARMENTS:
                cnt[lbl] += 1
    # highest count, tiebreak alphabetically; fallback 'unknown'
    return max(cnt.items(), key=lambda kv: (kv[1], kv[0]))[0] if cnt else "unknown"
# ----------------------
# 2.5) Filename reordering helper
# ----------------------
def reorder_filename(filename):
    """
    Given a filename base (no extension), split on '_', find the last all-digit token,
    remove it from its position, and prepend it to the rest.
    e.g. "2024-12-03_16-31-31_260311_40441309_vis" →
         "40441309_2024-12-03_16-31-31_260311_vis"
    """
    parts = filename.split('_')
    # find last token consisting only of digits
    idx = None
    for i, p in enumerate(parts):
        if p.isdigit():
            idx = i
    if idx is None:
        return filename  # nothing to reorder
    tok = parts.pop(idx)
    return '_'.join([tok] + parts)


# ----------------------
# 3) Splitting + exporting (with renaming)
# ----------------------
def split_and_export(groups, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    stats = {'groups': 0, 'sets': 0, 'discarded': 0, 'failures': []}

    for iid, pairs in groups.items():
        stats['groups'] += 1
        try:
            total = len(pairs)
            n_sets = total // 4
            if n_sets == 0:
                stats['discarded'] += total
                continue

            pairs.sort(key=lambda x: os.path.basename(x[0]))
            used = set()

            for k in range(n_sets):
                if k == 0:
                    selected = pick_first_four(pairs)
                else:
                    rem = [p for p in pairs if p not in used]
                    selected = rem[:4]

                for p in selected:
                    used.add(p)

                # detect garment & create folder
                garment = detect_garment([js for _, js in selected])
                out_name = f"{garment}_group_{iid}_set{k+1}"
                dst_dir = os.path.join(output_dir, out_name)
                os.makedirs(dst_dir, exist_ok=True)

                # copy with renamed filenames
                for src in selected:
                    for path in src:  # src is (jpg, json)
                        base, ext = os.path.splitext(os.path.basename(path))
                        new_base = reorder_filename(base)
                        dst_path = os.path.join(dst_dir, new_base + ext)
                        shutil.copy2(path, dst_path)

                stats['sets'] += 1

            stats['discarded'] += (total - n_sets*4)

        except Exception as e:
            stats['failures'].append(f"{iid}: {e}")

    return stats


# ----------------------
# 4) Main
# ----------------------
if __name__ == "__main__":
    BASE      = "/mnt/datasets/nips/hrnet"
    OUTPUT    = "/mnt/datasets/nips/hrnet_grouped"
    MIN_GSIZE = 4

    groups = gather_groups(BASE, min_group_size=MIN_GSIZE)
    stats  = split_and_export(groups, OUTPUT)

    print(f"→ Processed {stats['groups']} source groups")
    print(f"→ Created {stats['sets']} sets of 4")
    print(f"→ Discarded {stats['discarded']} leftover tuples")
    if stats['failures']:
        print("Failures:")
        for f in stats['failures']:
            print(" -", f)
