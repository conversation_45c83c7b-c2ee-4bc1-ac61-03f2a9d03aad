"""
# File: object_masker.py
# Wrapper class for mask detection.
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
# - The ObjectMasker class in Python is an utility designed for object detection and visualization using a pre-trained
# instance segmentation model. This class can detect and visualize multiple object classes in images, and allows
# customization of the detection threshold and visualization style.
# - Key methods in this class are detect to perform object detection, _draw_detections to overlay masks on detected
# objects, and print_detections to print details of detections. The class also maintains a record of detections, and
# supports processing on a GPU.
# - It requires a pre-trained model, a class map for object classes, and relevant image data
# for detection tasks.
# Changelog
#   04.11.2022. Javier <PERSON>. Update display setting to allows print text near to mask
#   11.11.2022. <PERSON>. Added minimum confidence in constructor
#   07.07.2023. <PERSON>. Review to make it more robust, including error handling.
#   11.04.2024. <PERSON>. Renamed to DetectorHR
"""
import warnings
from datetime import time

import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed

from dev.data_converter import yaml_file_to_dict
from dev.image_processing import read_images, display_plt
from dev.timer import Timer
from hrnet import Hrnet

warnings.simplefilter(action='ignore', category=FutureWarning)

import logging
import cv2

import json

logger = logging.getLogger("DET")

import os


class DetectorHR:

    def __init__(self, class_map, model_path, min_confidence=0.5, image_size=(600, 450), gpu=0, model_args={}):
        """
        Default constructor
        :param class_map: Dictionary that defines the style of each class when visualizing detection. Example
                        "<class_name>":{
                                "color_bgr": array[3] #array of 3 integers from 0 to 255
                                "thickness": float, #Negative value fills rectangle; positive draws empty rectangle
                                "display_text": bool, # TRUE if display class name and score; FALSE otherwise
                                "min_score": float, #The minimum score to be displayed
                        }
        :param min_confidence: The threshold used to filter out low-scored bounding boxes predicted
        :param model_path: Path to trained model
        :param image_size: Default image size. Prior to the prediction images will be resized
        :param gpu: Gpu id where the model is going to be loaded.
        :param args: (dict) Project specific parameter. For hrnet:
            - scales (list)
        """

        self.detections = []
        self.mode = "segmentation"

        if not os.path.isfile(model_path):
            raise IOError(f"'{model_path}' file not found!")

        # Set local variables
        self._image_size = image_size
        self.gpu = gpu
        self._min_confidence = min_confidence

        # Model specific
        scales = model_args.get("scales", [0.5])
        arch = model_args.get("arch", "network.ocrnet.HRNet")

        # Initialize color map
        if len(class_map) <= 1:
            raise ValueError("Not enough classes in class_map")

        self._class_map = class_map  # Color, type, thickness [>0; contour] [<0 mask], draw text (score), min confidence
        palette = [c for color, _, _, _, _ in self._class_map.values() for c in color] + [0] * (
                256 * 3 - len(self._class_map) * 3)

        self._model = Hrnet(model_path=model_path, palette=palette, scales=scales, num_classes=len(self._class_map),
                            gpu=gpu, arch=arch)

        print(f'[INFO] Loaded network {model_path}')

    def _calculate_scores(self, mask, probs) -> dict:
        """
        Calculate the scores of a given mask
        :param mask: image mask returned by the NeuronalNet.
        :param probs: Per pixel probabilities.
        :return: The average scores of a given masked ared
        """
        mask_probs = np.ma.array(probs, mask=mask)
        r = 0
        try:
            r = round(mask_probs.mean(), 3)
        except Exception as ex:
            print(f"{ex}\n{mask_probs}")

        return r

    def detect(self, images, class_names=[], images_in_memory=True, include_no_detections=True,
               store_visualizations=False, draw_masks=False, draw_bbox=False, store_bbox=False, store_scores=False, merge_garment=True):
        """
        This method stores a list of detections from a given set of images.
        :param store_visualizations: Store visualization (image+mask overlap) on self. Detections
        :param include_no_detections: Include entry in detection even if nothing was detected
        :param images: Path to each image or cv2 image object list to be processed
        :param threshold: Minimum score of a detection to be stored
        :param class_names: Name of the classes to be detected. Empty will detect all the classes.
        :param images_in_memory: True if you want to store original images in memory; False otherwise
        :returns detections dictionary. It is composed by a common entries such as original image or visualization
         image as well as path to the file and a set of arrays where each position of the array correspond with a single
        detection. For example, classes[0] -> boxes[0] -> scores[0] belong to the same detection.
            self.detections{
                    "classes"       -> Array of classes
                    "boxes"         -> Array of bounding boxes (xmin, ymin, xmax, max)
                    "scores"        -> Array of scores
                    "image"         -> Original image if flag images_in_memory is TRUE; "None" otherwise
                    "visualization" -> Image with bounding boxes, classes and scored drawn
                    "path"          -> path to the original files if @images are path; None otherwise
                    "masks"         -> a list containing the contours of every detected instance
        """
        detections = []
        for i, img in enumerate(images):
            try:
                image, path = (cv2.imread(img), img) if isinstance(img, str) else (img, None)

                # Raw detections
                masks, max_pro = self._model.evaluate([image])
                mask = masks[0]

                aux_classes, aux_masks = self._extract_masks(mask, merge_garment=merge_garment, min_area=1000)
                aux_scores = None if not store_scores else [self._calculate_scores(m, max_pro) for m in aux_masks]
                aux_boxes = None if not store_bbox else [self.mask_to_bbox(m) for m in aux_masks]

                if store_visualizations and store_bbox:
                    image_vis = image.copy() if store_visualizations else None

                    for i, box in enumerate(aux_boxes):
                        cls_name = aux_classes[i]
                        cls_score = str(aux_scores[i]) if store_scores else "N/A"
                        text = f"{cls_name}: {str(aux_scores[i])}" if store_scores else cls_name

                        bbox = box if draw_bbox else None
                        msk = aux_masks[i] if draw_masks else None

                        image_vis = self._draw_detections(image_vis, mask=msk, bbox=bbox, text=text,
                                                          color=self._class_map[cls_name][0])
                else:
                    image_vis = None
                # Visualize detections for each class
                if not images_in_memory:
                    image = None

                # Will be stored only if any detection that match the requirement is found
                # This will save memory
                detection_found = False
                if len(aux_classes) > 0 or include_no_detections:
                    detection_found = True

                    detection = {
                        "path": None,
                        "masks": aux_masks,
                        "boxes": aux_boxes,
                        "scores": aux_scores,
                        "classes": aux_classes,
                        "image": image,
                        "visualization": image_vis,
                        "mask": mask
                    }

                if detection_found:
                    detections.append(detection)

                del detection

            except Exception as ex:
                print("[ERROR] %s" % ex)
                raise ex

        return detections

    def _extract_masks(self, mask, merge_garment=True, min_area=10000, workers=8) -> tuple:
        """
        Split a given merged mask into single masks.

        :param mask: image mask returned by the NeuronalNet.
        :param merge_garment: whether to merge the masks to generate a single garment mask
        :param min_area: minimum area to consider.
        :param workers: number of concurrent workers.
        :return: (list of classes, list of masks). The masks are in graycalse
        """

        def process_class(c_name, c_color, c_type):
            if c_type == "background":
                return []
            if c_type == "defect":
                used_min_area = 0
            else:
                used_min_area = min_area

            c_color_a = np.array(c_color, dtype=np.uint8)
            merged_mask = cv2.inRange(mask, c_color_a, c_color_a)

            # if not np.any(merged_mask) or (merge_garment and c_type == "garment"):
            if merge_garment and c_type == "garment":
                return [[c_name, merged_mask, c_type, cv2.countNonZero(merged_mask)]]

            # print (f"{c_name}/{c_type}: {used_min_area}/{cv2.countNonZero(merged_mask)}")

            result = []

            contours, _ = cv2.findContours(merged_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for c in contours:
                area = cv2.contourArea(c)
                if area > used_min_area:

                    result.append((c_name, c, c_type, area))

            return result

        aux_contours = []
        aux_classes = []
        h, w, _ = mask.shape

        garment_cls = {}
        with ThreadPoolExecutor(max_workers=workers) as executor:
            futures = [executor.submit(process_class, c_name, c_info[0], c_info[1]) for c_name, c_info in
                       self._class_map.items()]
            for future in as_completed(futures):
                result = future.result()
                if result:
                    for c_name, c_mask, c_type, area in result:
                        if c_type == "garment":
                            garment_cls[c_name] = garment_cls.get(c_name, 0) + area
                        else:
                            aux_classes.append(c_name)
                            aux_contours.append(c_mask)
        if merge_garment:
            c_name = max(garment_cls, key=garment_cls.get)
            _, binary_mask = cv2.threshold(cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY), 1, 255, cv2.THRESH_BINARY)
            c_mask = cv2.dilate(binary_mask, np.ones((20, 20), np.uint8), iterations=1)
            
            contours, _ = cv2.findContours(c_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if len(contours )> 0:
                aux_classes.append(c_name)
                aux_contours.append(max(contours, key=cv2.contourArea))

        return aux_classes, aux_contours

    def mask_to_bbox(self, mask):
        contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        bbox = []
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)
            # (xmin, ymin, xmax, ymax)
            bbox.append([x, y, x + w, y + h])
        return bbox

    def _draw_detections(self, img, mask=None, bbox=None, text="", thickness=2, color=[255, 255, 255]):
        """
        Draw detected mask on the original image.
        :param img: Source image where the bounding boxes will be drawn
        :param mask: Detection mask
        :param text: Text to be written over the mask. Usually 'class score'
        :param thickness: The size of the mask (>0) or contour (<0)
        :param color: The color of the mask
        :return: The original image with the mask overlap
        """

        im = img.copy()

        if bbox is not None:
            if thickness < 0:
                overlay = img.copy()
                im = cv2.rectangle(img, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, thickness)
                cv2.addWeighted(overlay, -1 * thickness, im, 1 + thickness, 0, im)
            else:
                im = cv2.rectangle(img, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, thickness)

            if text != "":
                im = cv2.putText(img, '{:s}'.format(text), (int(bbox[0] - 2), int(bbox[1]) - 2),
                                 cv2.FONT_HERSHEY_SIMPLEX, 1, [255, 255, 255], 2)

        if mask is not None:
            # Ensure the mask is single-channel for proper processing:
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)

            if bbox is None and (text != "" or thickness >= 0):
                contours = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)[0]

            if thickness < 0:
                _, binary_mask = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY)
                color_layer = np.full(img.shape, color, dtype=np.uint8)
                blended = cv2.addWeighted(img, 0.5, color_layer, 0.5, 0)

                # Copy only the blended areas where the binary mask is not zero
                im[binary_mask > 0] = blended[binary_mask > 0]
            elif len(contours) > 0:
                cv2.drawContours(im, contours, -1, color, int(thickness))

                if bbox is None:
                    bbox = cv2.boundingRect(contours[0])

                im = cv2.putText(im, '{:s}'.format(text), (int(bbox[0] - 2), int(bbox[1]) - 2),
                                 cv2.FONT_HERSHEY_SIMPLEX, 0.5, [255, 255, 255], 1)

        return im

    @staticmethod
    def generate_annotation(detection: dict, epsilon_factor=0.005, mode="segmentation"):
        """
        Generate annotation data from detection results.

        :param detection: Dictionary containing detection results, including image path, image data,
                          bounding boxes, scores, masks, and classes.
        :param epsilon_factor: (int) Increase this value to reduce the number of points

        :return: List of annotations in JSON format.
        """
        img_filename = None if detection["path"] is None else os.path.basename(detection["path"])
        img_width, img_height = detection["image"].shape[1], detection["image"].shape[0]
        shapes = []

        for i in reversed(range(len(detection["classes"]))):

            mask = detection.get("masks", [])[i] if detection.get("masks") else None
            box = detection.get("boxes", [])[i] if detection.get("boxes") else None
            class_name = detection["classes"][i]

            if mode == "detection" and box is not None:
                points = [
                    [int(box.xyxy[0][0]), int(box.xyxy[0][1])],
                    [int(box.xyxy[1][0]), int(box.xyxy[0][1])],
                    [int(box.xyxy[1][0]), int(box.xyxy[1][1])],
                    [int(box.xyxy[0][0]), int(box.xyxy[1][1])]
                ]
                shape_type = "rectangle"
            elif mode == "segmentation" and mask is not None:
                # Approximate the contour
                approx_contour = cv2.approxPolyDP(mask, epsilon_factor * cv2.arcLength(mask, True), True)

                points = []
                # Iterate over each point in the approximated contour
                for pt in approx_contour:
                    # Convert the point to integers and add to the points list
                    points.append(list(map(int, pt[0])))

                shape_type = "polygon"
            else:
                continue

            shapes.append({
                "label": class_name,
                "points": points,
                "group_id": None,
                "shape_type": shape_type
            })

        return {
            "version": "5.0.1",
            "flags": {},
            "imageData": None,
            "shapes": shapes,
            "imagePath": img_filename if mode == "segmentation" else detection["path"],
            "imageHeight": img_height,
            "imageWidth": img_width

        }

if __name__ == '__main__':

    # conf = yaml_file_to_dict("/mnt/data/projects/hrnet/configs/eval_nips.yml / /mnt/data/projects/hrnet/configs/train_nips.yml")

    # Extract color palette from color map
    class_map = {
        "background": [[0, 0, 0], "background", 0, False, 0.9],
        "pants": [[128, 0, 0], "textile", 50, False, 0.4],
        "jacket": [[0, 128, 0], "textile", 50, False, 0.4],
        "overall": [[0, 0, 128], "textile", 50, False, 0.4],
        "t-shirt": [[128, 128, 0], "textile", 50, False, 0.4],
        "vest": [[128, 0, 128], "textile", 50, False, 0.4],
        "polo": [[0, 128, 128], "textile", 50, False, 0.4],
        "apron": [[128, 128, 128], "textile", 50, False, 0.4],
        "shirt": [[255, 0, 0], "textile", 50, False, 0.4],
        "pullover": [[0, 255, 0], "textile", 50, False, 0.4],
        "stain": [[0, 0, 255], "defect", 50, False, 0.4],
        "hole": [[255, 255, 0], "defect", 50, False, 0.4],
        "patch": [[255, 0, 255], "defect", 50, False, 0.4],
        "reflex_stripe": [[0, 255, 255], "defect", 50, False, 0.4],
        "reflex_normal": [[255, 128, 0], "defect", 50, False, 0.4],
        "damaged": [[128, 255, 0], "defect", 50, False, 0.4],
        "falloff": [[128, 0, 255], "defect", 50, False, 0.4],
        "reflex_prismen": [[255, 0, 128], "defect", 50, False, 0.4],
        "reflex_fabric": [[128, 128, 255], "defect", 50, False, 0.4]
    }
    # model_path = "backend/models/cizeta/"+conf["WEIGHTS"]
    model_path = "/mnt/data/projects/hrnet/logs/train_nips/ocrnet.HRNet_Mscale_rose-termite_2024.05.16_00.08/last_checkpoint_ep3158.pth"
    # model_path = "/mnt/data/projects/defis/backend/bucket/wsp/20230116_wsp_615.pth"
    # imgs_folder_path = "backend/bucket/dataset/stockings_aug/img"
    # imgs_folder_path = "/mnt/data/projects/defis/backend/bucket/wsp/images"
    imgs_folder_path = "/mnt/data/projects/hrnet/dev/testing_data/"
    out_dir = "/mnt/data/projects/hrnet/dev/testing_data/"

    extractor = DetectorHR(class_map, model_path)
    print("Detector ready")

    print("Reading images from disk")
    images, paths = read_images(imgs_folder_path, depth=1, max_img=1000, load_images=True, format="jpg")
    assert len(paths) > 0, "Not enough images!"
    print("Images ready")

    for i, img in enumerate(images):
        if i > 9:
            break
        print(paths[i])
        det = extractor.detect([img], images_in_memory=True, store_visualizations=True)[0]
        print("Detection done \n", len(det["masks"]), len(det["classes"]))
        createJSON(paths[i], det["masks"], det["classes"], out_dir)

        # display_plt(img, det["visualization"], layout="vertical")
        # display_plt(img, det["visualization"], layout="vertical")

        # cv2.imwrite(f"/home/<USER>/projects/defis/output/test/{i}.jpg", det["visualization"])

    print("Done")
