# python convert_labelme_to_cvat.py
# python convert_labelme_to_cvat.py --input ~/Documents/JuanLuis/cvat/ejemplo_import --output ~/Documents/JuanLuis/cvat/cvat_output --zip ~/Documents/JuanLuis/cvat/taskname.zip
# python convert_labelme_to_cvat.py --input ./data --output ./output --zip ./taskname.zip

import os
import json
import xml.etree.ElementTree as ET
import zipfile
import argparse
from glob import glob
import shutil
from datetime import datetime

def create_xml(labelme_data, output_dir):
    """
    Converts a LabelMe JSON file to CVAT-compatible XML,
    skipping polygons with fewer than 3 points.
    Always writes an XML file, even if no valid shapes are found.
    """
    root = ET.Element("annotation")

    image_filename = labelme_data.get("imagePath", "unknown.jpg")
    ET.SubElement(root, "folder").text = ""  # Avoid errors in CVAT
    ET.SubElement(root, "filename").text = image_filename

    source = ET.SubElement(root, "source")
    ET.SubElement(source, "sourceImage").text = "The MIT-CSAIL database of objects and scenes"
    ET.SubElement(source, "sourceAnnotation").text = "LabelMe Webtool"

    imagesize = ET.SubElement(root, "imagesize")
    ET.SubElement(imagesize, "nrows").text = str(labelme_data.get("imageHeight", 0))
    ET.SubElement(imagesize, "ncols").text = str(labelme_data.get("imageWidth", 0))

    for i, shape in enumerate(labelme_data.get("shapes", [])):
        points = shape.get("points", [])
        if len(points) < 3:
            print(f"⚠️ Skipping shape in '{image_filename}' with < 3 points: {points}")
            continue

        obj = ET.SubElement(root, "object")
        ET.SubElement(obj, "name").text = shape.get("label", "unknown")
        ET.SubElement(obj, "id").text = str(i)
        ET.SubElement(obj, "deleted").text = "0"
        ET.SubElement(obj, "verified").text = "1"
        ET.SubElement(obj, "occluded").text = "no"
        ET.SubElement(obj, "date").text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        polygon = ET.SubElement(obj, "polygon")
        ET.SubElement(polygon, "username").text = "anonymous"
        for point in points:
            pt = ET.SubElement(polygon, "pt")
            ET.SubElement(pt, "x").text = str(int(point[0]))
            ET.SubElement(pt, "y").text = str(int(point[1]))

    xml_filename = os.path.join(output_dir, os.path.splitext(os.path.basename(image_filename))[0] + ".xml")
    tree = ET.ElementTree(root)
    tree.write(xml_filename, encoding="utf-8", xml_declaration=True)

    print(f"✅ XML generado: {xml_filename}")
    return xml_filename



def clear_output_folder(output_folder):
    """
    Limpia la carpeta de salida eliminando todos los archivos y subcarpetas.
    """
    if os.path.exists(output_folder):
        for root, dirs, files in os.walk(output_folder, topdown=False):
            for file in files:
                os.remove(os.path.join(root, file))
            for dir in dirs:
                os.rmdir(os.path.join(root, dir))
        print(f"✅ Carpeta de salida '{output_folder}' limpiada.")
    else:
        os.makedirs(output_folder)
        print(f"✅ Carpeta de salida '{output_folder}' creada.")

def convert_labelme_to_cvat(input_folder, output_folder, zip_output):
    """
    Convierte JSON de LabelMe en XML compatible con CVAT.
    Luego genera un archivo ZIP incluyendo las imágenes originales.
    """
    clear_output_folder(output_folder)  # Limpia la carpeta de salida

    json_files = glob(os.path.join(input_folder, "*.json"))

    for json_file in json_files:
        try:
            with open(json_file, "r", encoding="utf-8") as f:
                labelme_data = json.load(f)

            result = create_xml(labelme_data, output_folder)
            if not result:
                print(f"❌ No valid shapes found in {json_file}, skipping image.")
                continue

            image_filename = labelme_data.get("imagePath")
            if image_filename:
                image_path = os.path.join(input_folder, image_filename)
                if os.path.exists(image_path):
                    shutil.copy(image_path, os.path.join(output_folder, os.path.basename(image_filename)))
        except Exception as e:
            print(f"❌ Error procesando {json_file}: {e}")

    try:
        with zipfile.ZipFile(zip_output, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        zipf.write(file_path, os.path.relpath(file_path, output_folder))
                    except Exception as e:
                        print(f"❌ Error añadiendo {file_path} al ZIP: {e}")
    except Exception as e:
        print(f"❌ Error creando el archivo ZIP: {e}")

    print(f"✅ Conversión completa. Archivo ZIP guardado en {zip_output}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert LabelMe JSON annotations to CVAT XML format.")
    parser.add_argument("--input", type=str, default=".", help="Folder with LabelMe JSON annotations")
    parser.add_argument("--output", type=str, default="cvat_annotations", help="Output folder for XML files")
    parser.add_argument("--zip", type=str, default="taskname.zip", help="Output ZIP file name")

    args = parser.parse_args()

    convert_labelme_to_cvat(args.input, args.output, args.zip)
    print(f"✅ Conversión completa. Archivo ZIP guardado en {args.zip}")



