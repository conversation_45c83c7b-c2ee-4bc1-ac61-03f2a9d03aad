"""
# File: data_converter.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
    Collection of utilities to manage data from documents such as xml, json and yaml.
    It also provides function to convert from/to python dictionaries
# Changelog:
#   - 14.10.2023. <PERSON>. Implemented json_string_to_dict
"""
import json
from pathlib import Path

import xmltodict
import yaml
import csv


def xml_string_to_dict(xmlstr: str) -> dict:
    """Convert an XML string to a Python dictionary."""
    try:
        return xmltodict.parse(xmlstr)
    except Exception as e:
        raise ValueError("Failed to parse XML string. Error: {}".format(e))


def xml_file_to_dict(xml_path: str) -> dict:
    """Reads XML file and convert it to a Python dictionary."""
    if not Path(xml_path).is_file():
        raise FileNotFoundError(f"File '{xml_path}' does not exist.")
    try:
        with open(xml_path, "r") as f:
            return xml_string_to_dict(f.read())
    except Exception as e:
        raise ValueError("Failed to parse XML file. Error: {}".format(e))


def dict_to_xml_string(data: dict) -> str:
    """Parse a dictionary into an XML string."""
    try:
        return xmltodict.unparse(data, pretty=True)
    except Exception as e:
        raise ValueError("Failed to convert dictionary to XML string. Error: {}".format(e))


def dict_to_xml_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into an XML and save it to the disk."""
    xml_string = dict_to_xml_string(data)

    # Ensure parent folder exist
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(xml_string)
    except Exception as e:
        raise ValueError("Failed to write XML to file. Error: {}".format(e))


def json_string_to_dict(json_str: str) -> dict:
    """Convert a JSON string to a Python dictionary."""
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError("Failed to decode JSON string. Error: {}".format(e))


def json_file_to_dict(json_path: str) -> dict:
    """Reads JSON file and convert it to a Python dictionary."""
    if not Path(json_path).is_file():
        raise FileNotFoundError(f"File '{json_path}' does not exist.")
    try:
        with open(json_path, "r") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError("Failed to decode JSON file. Error: {}".format(e))


def dict_to_json_string(data: dict) -> str:
    """Parse a dictionary into a JSON string."""
    try:
        return json.dumps(data, indent=4)
    except Exception as e:
        raise ValueError("Failed to convert dictionary to JSON string. Error: {}".format(e))


def dict_to_json_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into a JSON and save it to the disk."""
    json_string = dict_to_json_string(data)

    # Ensure parent folder exist
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(json_string)
    except Exception as e:
        raise ValueError("Failed to write JSON to file. Error: {}".format(e))


def yaml_file_to_dict(path: str) -> dict:
    """Load a YAML configuration file."""
    if not Path(path).is_file():
        raise FileNotFoundError(f"The configuration file '{path}' does not exist.")

    with open(path, 'r') as file:
        try:
            return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ValueError(f"The configuration file '{path}' is not a valid YAML file. Error: {str(e)}")


def dict_to_yaml_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into YAML and save it to the disk."""
    yaml_string = yaml.dump(data, default_flow_style=False)

    # Ensure parent folder exists
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(yaml_string)
    except Exception as e:
        raise ValueError("Failed to write YAML to file. Error: {}".format(e))


def csv_file_to_dict(path: str) -> dict:
    """
    Load a CSV file and convert it to a dictionary.

    :param path: The path to the CSV file
    :return: The CSV data as a dictionary
    """
    if not Path(path).is_file():
        raise FileNotFoundError(f"The file '{path}' does not exist.")

    with open(path, 'r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header
        result = {}
        for row in reader:
            key = row[0]
            value = row[1:]
            if key in result:
                # If the key already exists, append the new value to the existing list
                result[key].append(value) if len(value) > 1 else result[key].append(value[0])
            else:
                # If the key does not exist, add it to the dictionary
                result[key] = value if len(value) > 1 else value[0]
        return result
