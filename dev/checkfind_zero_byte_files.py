#!/usr/bin/env python3
"""
Script to find zero-byte files in a directory tree.
"""
import os

def find_zero_byte_files(root_dir):
    """Walk through root_dir and print paths of all files of size 0 bytes."""
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            try:
                if os.path.getsize(file_path) == 0:
                    print(f"Empty file: {file_path}")
            except OSError as e:
                print(f"Error accessing {file_path}: {e}")

if __name__ == "__main__":
    root_dir = '/mnt/datasets/nips/WSP_test_set'
    find_zero_byte_files(root_dir)
    print("Done.")
