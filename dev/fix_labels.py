#!/usr/bin/env python3
"""
Fix wrong `label` fields inside NIPS-HRNet JSON annotations.

▪ Reads the canonical class-map YAML.
▪ Finds every *.json file under /mnt/datasets/nips/hrnet/.
▪ Asks you how to translate each unknown label once.

Run:
    python fix_labels.py
"""
import json
import os
import shutil
import glob
from pathlib import Path
import yaml

# --------------------------------------------------------------------------- #
# Configuration – adjust if your paths differ
# --------------------------------------------------------------------------- #
DATASET_ROOT   = Path("/mnt/datasets/nips/hrnet_test")          # where the *.json live
CLASSMAP_PATH  = Path("/mnt/data/projects/hrnet/configs/nips/class_map.yaml")
# --------------------------------------------------------------------------- #


def load_allowed_labels(yaml_path: Path) -> set[str]:
    """Return the set of legal labels from class_map.yaml."""
    with yaml_path.open("r", encoding="utf-8") as fh:
        class_map = yaml.safe_load(fh)
    return set(class_map.keys())


def find_json_files(root: Path) -> list[Path]:
    """Return all *.json below *root* (recursively)."""
    pattern = str(root / "**" / "*.json")
    return [Path(p) for p in glob.glob(pattern, recursive=True)]


def collect_unknown_labels(json_files: list[Path], allowed: set[str]) -> set[str]:
    """Return every label string that is not allowed."""
    unknown = set()
    for jf in json_files:
        try:
            with jf.open("r", encoding="utf-8") as fh:
                data = json.load(fh)
            for shape in data.get("shapes", []):
                lbl = shape.get("label", "").strip()
                if lbl and lbl not in allowed:
                    unknown.add(lbl)
        except (json.JSONDecodeError, OSError) as exc:
            print(f"[!] Could not read {jf}: {exc}")
    return unknown


def prompt_replacements(bad_labels: set[str]) -> dict[str, str]:
    """Ask the user once for each bad label -> new label mapping."""
    mapping = {}
    print("\nEnter the correct label for each unknown entry.\n"
          "Press <Enter> without text to skip a label (file will stay unchanged).")
    for bad in sorted(bad_labels):
        new = input(f'  • Replace "{bad}" with: ').strip()
        if new:
            mapping[bad] = new
    return mapping


def rewrite_json_files(json_files: list[Path], mapping: dict[str, str]) -> None:
    """Apply *mapping* to every JSON file, rewriting labels in place."""
    for jf in json_files:
        changed = False
        try:
            with jf.open("r", encoding="utf-8") as fh:
                data = json.load(fh)

            for shape in data.get("shapes", []):
                lbl = shape.get("label", "").strip()
                if lbl in mapping:
                    shape["label"] = mapping[lbl]
                    changed = True

            if changed:
                print(f"[✓] Fixed {jf.relative_to(DATASET_ROOT)}")
        except (json.JSONDecodeError, OSError) as exc:
            print(f"[!] Could not update {jf}: {exc}")


def main() -> None:
    allowed = load_allowed_labels(CLASSMAP_PATH)
    json_files = find_json_files(DATASET_ROOT)
    unknown = collect_unknown_labels(json_files, allowed)

    if not unknown:
        print("No unknown labels found. Nothing to do.")
        return

    mapping = prompt_replacements(unknown)
    if not mapping:
        print("No replacements entered. Aborting.")
        return

    rewrite_json_files(json_files, mapping)
    print("Done.")


if __name__ == "__main__":
    main()
