import logging
import sys
import os
import threading
import gc
import json

from dev.data_converter import yaml_file_to_dict
from dev.gpu_processor import GPUProcessor
from dev.image_processing import read_images_batch
from dev.timer import Timer
from dev.detector_hr import DetectorHR as Detector

import cv2
import numpy as np

# Run with: PYTHONPATH=. python dev/prelabel_medical.py

# Configuration paths and constants
CONFIGS_PATH = "/mnt/data/projects/hrnet/configs/medical"
INPUT_DIR = "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/output/saved_images/2025-07-11/Sigvaris/first_batch"
OUTPUT_DIR = "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/output/saved_images/2025-07-11/prelabel"
MODEL_DIR = "/mnt/data/projects/hrnet/models/2025_07_11_medical.pth"
GPU = 0
BUFFER = 0
PROCESSED = []
_lock = threading.Lock()

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(name)-8.8s | %(levelname)-6.6s | %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("PRELABEL")


def overlay_detections(image: np.ndarray, masks: list, classes: list, class_map: dict) -> np.ndarray:
    """
    Overlay masks and class labels on the original image.
    """
    # Copy original image for overlays
    overlay = image.copy()

    # Generate unique random colors for each class label
    np.random.seed(42)
    colors = {cls: tuple(np.random.randint(0, 255, 3).tolist()) for cls in set(classes)}

    for mask, cls in zip(masks, classes):
        # Convert mask to binary
        binary_mask = (mask > 0).astype(np.uint8)
        color = colors[cls]

        # Create colored mask
        colored_mask = np.zeros_like(image, dtype=np.uint8)
        colored_mask[binary_mask == 1] = color

        # Blend colored mask onto overlay (50% transparency)
        cv2.addWeighted(colored_mask, 0.5, overlay, 0.5, 0, overlay)

        # Find and draw contours for sharper defect boundaries
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(overlay, contours, -1, color, 2)

        # Put class label text above the top-left corner of the contour
        if contours:
            x, y, _, _ = cv2.boundingRect(contours[0])
            label = class_map.get(cls, str(cls))
            cv2.putText(overlay, label, (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)

    return overlay


if __name__ == '__main__':
    # Load class map from YAML
    class_map = yaml_file_to_dict(os.path.join(CONFIGS_PATH, "class_map.yaml"))
    model_args = {
        "scales": [0.5],
        "arch": "network.ocrnet.HRNet_Mscale"
    }

    # Initialize detector with specified model and GPU
    detector = Detector(
        class_map=class_map,
        model_path=MODEL_DIR,
        model_args=model_args,
        gpu=GPU,
        image_size=(2918, 2100),
        min_confidence=0.1
    )

    # Main processing loop
    batch_id = 0
    batch_size = 100
    image_count = 0
    has_more = True

    while has_more:
        images, paths = read_images_batch(
            INPUT_DIR,
            batch_id=batch_id,
            batch_size=batch_size,
            load_images=True
        )

        # Process each image in the batch
        for image, path in zip(images, paths):
            # Perform detection
            detection = detector.detect(
                [image], images_in_memory=False, merge_garment=False, store_visualizations=False
            )[0]

            masks = detection.get('masks', [])
            classes = detection.get('classes', [])

            # Save JSON annotation
            detection["image"] = None
            detection["path"] = None
            annotation = Detector.generate_annotation(detection)
            json_filename = os.path.basename(path)[:-4] + ".json"
            with open(os.path.join(OUTPUT_DIR, json_filename), "w") as json_file:
                json.dump(annotation, json_file)

            # Overlay detections and save labeled image
            labeled_image = overlay_detections(image, masks, classes, class_map)
            labeled_filename = os.path.basename(path)[:-4] + "_labeled.jpg"
            cv2.imwrite(
                os.path.join(OUTPUT_DIR, labeled_filename),
                labeled_image,
                [int(cv2.IMWRITE_JPEG_QUALITY), 100]
            )

            image_count += 1
            logger.info(f"Processed {image_count}: {path}")

        # Check if this was the last batch
        if len(images) < batch_size:
            has_more = False
            logger.info("No more images to process.")

        batch_id += 1
        del images, paths
        gc.collect()

    logger.info("Image prelabeling with overlays complete.")
