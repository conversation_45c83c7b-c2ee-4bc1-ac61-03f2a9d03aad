import os
import tempfile
import shutil
import tarfile
import uuid
import zipfile
from smbprotocol.connection import Connection
from smbprotocol.session import Session
from smbprotocol.tree import TreeConnect
from smbprotocol.open import Open
from smbprotocol.open import FileInformationClass
from smbprotocol import exceptions
import json

# Global counter for opened JSON files
json_counter = 0
MAX_READ_CHUNK = 8 * 1024 * 1024  # 8 MB maximum read chunk
found_dirs = set()  # Track directories that contain at least one matching JSON file

def traverse_directory(tree, current_path, search_label):
    """
    Recursively traverse the SMB directory to search for JSON files containing a specific label.
    Print the directory name only once if it contains a matching JSON file.
    """
    global json_counter, found_dirs  # Track processed JSON files and directories

    json_count = 0  # Track JSON files in this directory
    matching_json_found = False  # Flag to track if we already printed the directory

    smb_dir = Open(tree, current_path)
    smb_dir.create(
        desired_access=0x1,  # Read access
        file_attributes=0x80,  # Normal file
        share_access=0x7,  # Allow read, write, delete
        create_disposition=0x1,  # Open existing
        create_options=0x200000,  # Directory
        impersonation_level=0x2  # Impersonation
    )

    try:
        all_entries = []
        try:
            while True:
                entries = smb_dir.query_directory(
                    pattern="*",
                    file_information_class=FileInformationClass.FILE_DIRECTORY_INFORMATION
                )
                if not entries:
                    break  # Stop if no files are returned
                all_entries.extend(entries)
                if len(entries) < 100:  # Some servers return results in chunks, break if <100
                    break
        except exceptions.NoMoreFiles:
            pass

        for entry in all_entries:
            file_name = entry.fields["file_name"].get_value().decode("utf-16-le")
            is_directory = entry.fields["file_attributes"].has_flag(0x10)  # Check if it's a directory

            if file_name in [".", ".."]:
                continue  # Skip system entries

            full_path = f"{current_path}/{file_name}"

            if is_directory:
                traverse_directory(tree, full_path, search_label)

            elif file_name.endswith(".json"):
                json_count += 1  # Count JSON files
                json_counter += 1  # Global counter

                try:
                    if process_json(tree, full_path, search_label):  
                        if not matching_json_found:
                            print(f"Label '{search_label}' found in directory: {current_path}")
                            found_dirs.add(current_path)  # Mark the directory as printed
                            matching_json_found = True  # Avoid duplicate prints for this directory

                except Exception as e:
                    print(f"Error processing JSON {full_path}: {e}")  # Log errors

            elif file_name.endswith((".zip", ".tgz", ".tar.gz")):
                try:
                    if process_compressed_file(tree, full_path, search_label):
                        if not matching_json_found:
                            print(f"Label '{search_label}' found in directory: {current_path}")
                            found_dirs.add(current_path)
                            matching_json_found = True  # Avoid duplicate prints for this directory
                except Exception as e:
                    print(f"Error processing compressed file {full_path}: {e}")  # Log errors

    finally:
        smb_dir.close()

def process_json(tree, json_path, search_label):
    """
    Process a JSON file and check if it contains the specified label.
    Returns True if the label is found, otherwise False.
    """
    json_file = Open(tree, json_path)
    json_file.create(
        desired_access=0x120089,
        file_attributes=0x80,
        share_access=0x7,
        create_disposition=0x1,
        create_options=0x0,
        impersonation_level=0x2
    )

    try:
        file_size = json_file.end_of_file
        if file_size == 0:
            return False  # Skip empty files

        content = bytearray()
        offset = 0
        while offset < file_size:
            chunk_size = min(MAX_READ_CHUNK, file_size - offset)
            content.extend(json_file.read(offset=offset, length=chunk_size))
            offset += chunk_size

        try:
            data = json.loads(content.decode("utf-8"))
            if isinstance(data, dict) and "shapes" in data:
                for shape in data["shapes"]:
                    if shape.get("label") == search_label:
                        return True  # Label found
        except (json.JSONDecodeError, UnicodeDecodeError):
            return False  # Ignore JSON errors
    finally:
        json_file.close()

    return False  # No matching label found

def process_compressed_file(tree, compressed_path, search_label):
    """
    Process a compressed file (e.g., .zip, .tgz) and search for the specified label.
    Returns True if the label is found, otherwise False.
    """
    temp_dir = tempfile.mkdtemp()

    try:
        # Read and save the compressed file locally
        compressed_file = Open(tree, compressed_path)
        compressed_file.create(
            desired_access=0x120089,
            file_attributes=0x80,
            share_access=0x7,
            create_disposition=0x1,
            create_options=0x0,
            impersonation_level=0x2
        )

        local_compressed_file = os.path.join(temp_dir, os.path.basename(compressed_path))
        with open(local_compressed_file, "wb") as local_file:
            file_size = compressed_file.end_of_file
            offset = 0
            while offset < file_size:
                read_length = min(64 * 1024, file_size - offset)
                local_file.write(compressed_file.read(offset=offset, length=read_length))
                offset += read_length

        # Extract the compressed file
        extracted_dir = os.path.join(temp_dir, "extracted")
        os.makedirs(extracted_dir, exist_ok=True)

        if local_compressed_file.endswith(".zip"):
            with zipfile.ZipFile(local_compressed_file, "r") as zip_ref:
                zip_ref.extractall(extracted_dir)
        elif local_compressed_file.endswith((".tgz", ".tar.gz")):
            with tarfile.open(local_compressed_file, "r:gz") as tar_ref:
                tar_ref.extractall(extracted_dir)

        # Process extracted JSON files
        for root, _, files in os.walk(extracted_dir):
            for file in files:
                if file.endswith(".json"):
                    json_file_path = os.path.join(root, file)
                    with open(json_file_path, "r") as json_file:
                        try:
                            data = json.load(json_file)
                            if isinstance(data, dict) and "shapes" in data:
                                for shape in data["shapes"]:
                                    if shape.get("label") == search_label:
                                        return True  # Label found
                        except json.JSONDecodeError:
                            pass
    finally:
        shutil.rmtree(temp_dir)

    return False  # No matching label found
def find_label_in_smb(server, share, username, password, root_directory, search_label):
    """
    Connect to the SMB server and search for JSON files containing a specific label.
    :param server: SMB server address
    :param share: SMB share name
    :param username: SMB username
    :param password: SMB password
    :param root_directory: Root directory path on the SMB share to start the search
    :param search_label: Label to search for in the JSON files
    """
    global json_counter

    # Generate a valid UUID for the client GUID
    client_guid = uuid.uuid4()

    # Connect to the SMB server
    connection = Connection(
        guid=client_guid,
        server_name=server,
        port=445,
        require_signing=True
    )

    # Configure maximum sizes for optimized read/write operations
    connection.max_read_size = 64 * 1024 * 1024
    connection.max_write_size = 64 * 1024 * 1024
    connection.max_transact_size = 64 * 1024 * 1024

    connection.connect()

    try:
        session = Session(connection, username, password)
        session.connect()

        tree = TreeConnect(session, share)
        tree.connect()

        traverse_directory(tree, root_directory, search_label)

        print(f"Total JSON files processed: {json_counter}")
    finally:
        connection.disconnect()

# Replace these with your SMB credentials and details
server = "*********"
share = "shared"
username = "simon"
password = "4256"
root_directory = "projects/nips_cws/04_software/database/04_labeled"
search_label = "polo"  # Replace with the label you want to search for

find_label_in_smb(server, share, username, password, root_directory, search_label)