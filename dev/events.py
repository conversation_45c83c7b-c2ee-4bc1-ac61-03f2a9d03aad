"""
# File: image_processing.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
    A simple general purpose event handling class, which manages callbacks to be executed.
# Changelog:
    - 13.04.2021 - First commit
    - 21.08.2023 - Efficienty improvements
"""


from collections import defaultdict

class Events:
    def __init__(self):
        self.events = defaultdict(list)

    def on(self, event_name, callback):
        """
        Register a callback to an event.
        @param event_name: Register the name of the event 
        @param callback: The function to call when the event is triggered
        """
        if callback:  # None, False, empty are considered "Falsy"
            self.events[event_name].append(callback)

    def trigger(self, event_name, *args):
        """
        Trigger all callbacks registered to the specified event.
        @param event_name: Name of the event to trigger
        @param args: Arguments to pass to the callbacks
        """
        if event_name not in self.events:
            return
        for callback in self.events[event_name]:
            callback(*args)


