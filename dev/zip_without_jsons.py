import os
import zipfile

root_dir = "/mnt/datasets/nips/simon/raw_images"

for folder in os.listdir(root_dir):
    folder_path = os.path.join(root_dir, folder)
    
    if os.path.isdir(folder_path):  # Ensure it's a directory
        zip_path = os.path.join(root_dir, f"{folder}.zip")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if not file.endswith(".json"):  # Exclude JSON files
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, root_dir)  # Keep relative path inside zip
                        zipf.write(file_path, arcname)
        
        print(f"Zipped: {zip_path}")
