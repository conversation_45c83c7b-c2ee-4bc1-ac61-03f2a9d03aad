#!/usr/bin/env python3

import os
import json
import shutil
from pathlib import Path

# Define the target classes
TARGET_CLASSES = {
    "broken_stitches",
    "dirty-soil-stain",
    "open_seam",
    "puckering-fullness",
    "thread_end_fly_thread"
}

INPUT_DIR = Path("/mnt/datasets/tal/garment/database/batch6_cvat/")
OUTPUT_DIR = Path("/mnt/datasets/tal/garment/database/batch6_cvat_focus")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

def is_valid_annotation(json_data):
    """Check if annotation contains at least one target class."""
    return any(shape.get("label", "").lower() in TARGET_CLASSES for shape in json_data.get("shapes", []))

def filter_annotation(json_data):
    """Keep only shapes with target classes."""
    json_data["shapes"] = [s for s in json_data["shapes"] if s.get("label", "").lower() in TARGET_CLASSES]
    return json_data

def main():
    for root, _, files in os.walk(INPUT_DIR):
        for file in files:
            if not file.endswith(".json"):
                continue

            json_path = Path(root) / file
            with open(json_path, "r", encoding="utf-8") as f:
                try:
                    data = json.load(f)
                except Exception as e:
                    print(f"❌ Skipping corrupted JSON: {json_path} ({e})")
                    continue

            if not is_valid_annotation(data):
                continue  # skip if no relevant class

            # Filter annotation
            filtered_data = filter_annotation(data)

            # Copy JSON
            out_json_path = OUTPUT_DIR / file
            with open(out_json_path, "w", encoding="utf-8") as f:
                json.dump(filtered_data, f, indent=2)

            # Copy image
            image_filename = data.get("imagePath") or file.replace(".json", ".jpg")
            image_path = json_path.parent / image_filename
            if image_path.exists():
                shutil.copy2(image_path, OUTPUT_DIR / image_filename)
            else:
                print(f"⚠️ Image not found for: {json_path}")

if __name__ == "__main__":
    main()
