import os
import zipfile
from pathlib import Path

input_dir = Path("/mnt/datasets/tal/garment/database/all_focus_cvat")
output_dir = Path("/mnt/datasets/tal/garment/cvat")
max_package_size_bytes = 5 * 1024**3  # 5 GB

output_dir.mkdir(parents=True, exist_ok=True)

# Strip only the known suffix (.jpg or .xml)
def base_name(file: Path):
    return file.stem  # This works because filenames are like 20241219..._camera

# Build maps
images = {base_name(f): f for f in input_dir.glob("*.jpg")}
annotations = {base_name(f): f for f in input_dir.glob("*.xml")}

# Match image/annotation pairs
common_keys = sorted(set(images.keys()) & set(annotations.keys()))
pairs = [(images[k], annotations[k]) for k in common_keys]

print(f"Found {len(pairs)} image/annotation pairs.")

# Package into zip files of ≤ 5GB
current_zip = []
current_size = 0
package_index = 1

def create_zip(pairs_to_zip, index):
    zip_path = output_dir / f"package_{index:03}.zip"
    with zipfile.ZipFile(zip_path, "w", compression=zipfile.ZIP_DEFLATED) as zf:
        for file in pairs_to_zip:
            arcname = file.relative_to(input_dir)
            zf.write(file, arcname)
    print(f"Created: {zip_path}")

for img, ann in pairs:
    pair_size = img.stat().st_size + ann.stat().st_size

    if current_size + pair_size > max_package_size_bytes and current_zip:
        create_zip(current_zip, package_index)
        package_index += 1
        current_zip = []
        current_size = 0

    current_zip.extend([img, ann])
    current_size += pair_size

# Final package
if current_zip:
    create_zip(current_zip, package_index)
