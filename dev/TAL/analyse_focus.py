import os
import json
from collections import Counter
from glob import glob

# Directory containing LabelMe JSON files
labelme_dir = "/mnt/datasets/tal/garment/database/all_focus"

# Search for JSON files recursively
json_files = glob(os.path.join(labelme_dir, "**", "*.json"), recursive=True)

# Count defect labels
defect_counter = Counter()

for json_file in json_files:
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
            for shape in data.get("shapes", []):
                label = shape.get("label")
                if label:
                    defect_counter[label] += 1
    except Exception as e:
        print(f"Failed to read {json_file}: {e}")

# Print results
print("Defect counts:")
for label, count in defect_counter.most_common():
    print(f"{label}: {count}")
