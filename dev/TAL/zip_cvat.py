#!/usr/bin/env python3
"""
Bundle a CVAT export into ZIP archives organised like this:

    part1.zip
        package001/
            <original tree>/…/foo.jpg
            <original tree>/…/foo.xml
            ...
        package002/
        package003/
        package004/
        package005/
    part2.zip
        package006/
        …
        package010/
    ...

• Each *package* contains exactly N image/annotation pairs (default: 300).
• Each ZIP archive contains exactly M packages (default: 5).
• Pairs are jpg + xml files that share the same basename.
• Directory structure beneath the source root is preserved inside every package.
• JPG files without an XML counterpart are skipped with a warning.
"""

import argparse
import itertools
import os
import shutil
import sys
import tempfile
import zipfile
from pathlib import Path


def iter_jpgs(source_root: Path):
    """Yield all .jpg files under source_root, sorted case-insensitively."""
    return sorted(source_root.rglob("*.jpg"), key=lambda p: p.as_posix().lower())


def copy_pair(jpg: Path, pkg_dir: Path, source_root: Path):
    """
    Copy jpg and its matching xml (assumed to exist) into pkg_dir,
    preserving the relative path below source_root.
    """
    rel_path = jpg.relative_to(source_root)
    dst_jpg = pkg_dir / rel_path
    dst_jpg.parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(jpg, dst_jpg)

    xml = jpg.with_suffix(".xml")
    dst_xml = pkg_dir / xml.relative_to(source_root)
    dst_xml.parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(xml, dst_xml)


def zip_directory(src_dir: Path, zip_path: Path):
    """Create zip_path containing the entire contents of src_dir."""
    with zipfile.ZipFile(zip_path, "w", compression=zipfile.ZIP_DEFLATED) as zf:
        for file in src_dir.rglob("*"):
            if file.is_file():
                zf.write(file, file.relative_to(src_dir))


def main(
    source: Path,
    output_prefix: str,
    pairs_per_package: int,
    packages_per_zip: int,
):
    if not source.is_dir():
        sys.exit(f"Source directory {source} does not exist")

    package_no = 1   # global package counter
    part_no = 1      # zip counter
    pairs_in_current_package = 0
    packages_in_current_zip = 0

    # One temporary workspace reused for every zip
    with tempfile.TemporaryDirectory() as tmp_root_str:
        tmp_root = Path(tmp_root_str)
        pkg_dir = tmp_root / f"package{package_no:03d}"
        pkg_dir.mkdir(parents=True)

        for jpg in iter_jpgs(source):
            xml = jpg.with_suffix(".xml")
            if not xml.exists():
                print(f"⚠  Skipped (missing XML): {jpg.relative_to(source)}", file=sys.stderr)
                continue

            copy_pair(jpg, pkg_dir, source)
            pairs_in_current_package += 1

            # is the current package full?
            if pairs_in_current_package == pairs_per_package:
                print(f"📦  Finished package{package_no:03d}")
                package_no += 1
                pairs_in_current_package = 0
                packages_in_current_zip += 1

                # is the current zip full?
                if packages_in_current_zip == packages_per_zip:
                    zip_path = Path(f"{output_prefix}{part_no}.zip")
                    zip_directory(tmp_root, zip_path)
                    print(f"🗜️   Created {zip_path}  ({packages_in_current_zip} packages)")
                    part_no += 1
                    packages_in_current_zip = 0
                    shutil.rmtree(tmp_root)
                    tmp_root.mkdir()
                # make the next package directory (either in same zip or new zip)
                pkg_dir = tmp_root / f"package{package_no:03d}"
                pkg_dir.mkdir(parents=True)

        # any leftover pairs?
        if pairs_in_current_package == 0:
            # remove the unused empty package directory
            shutil.rmtree(pkg_dir, ignore_errors=True)
        else:
            packages_in_current_zip += 1

        # write the final zip if it contains anything
        if packages_in_current_zip:
            zip_path = Path(f"{output_prefix}{part_no}.zip")
            zip_directory(tmp_root, zip_path)
            print(f"🗜️   Created {zip_path}  ({packages_in_current_zip} packages)")

    print(f"✅  Done – total ZIPs: {part_no}, total packages: {package_no-1}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Bundle CVAT JPG/XML pairs into structured ZIP archives.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--source", type=Path, default=Path("cvat_fixed"),
                        help="root directory containing images and XMLs")
    parser.add_argument("--output-prefix", type=str,
                        default="cvat_tal_small_labelled_part",
                        help="prefix for the generated ZIP files")
    parser.add_argument("--pairs-per-package", type=int, default=300,
                        help="image+annotation pairs per package folder")
    parser.add_argument("--packages-per-zip", type=int, default=5,
                        help="how many packages go into one ZIP archive")

    args = parser.parse_args()
    main(args.source, args.output_prefix, args.pairs_per_package, args.packages_per_zip)
