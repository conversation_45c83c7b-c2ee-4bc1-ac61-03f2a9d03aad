#!/usr/bin/env python3
import os
import glob
import yaml
import cv2

base_folder = "/mnt/datasets/tal/garment/raw/images/batch_9/"
output_base_folder = "/mnt/datasets/tal/garment/raw/vis_batch9"
os.makedirs(output_base_folder, exist_ok=True)

# Define unique BGR colors for each defect class
defect_colors = {
    "Dirty-soil-stain":        (0, 0, 255),
    "Thread end/Fly Thread":   (0, 255, 0),
    "Run off stitches":        (255, 0, 0),
    "Broken Stitches":         (255, 255, 0),
    "Skip Stitches":           (255, 0, 255),
    "Twisted Roping":          (0, 255, 255),
    "Puckering - Fullness":    (128, 128, 255),
    "Poor shape":              (128, 255, 128),
    "Placket":                 (255, 128, 128),
    "Button (hole)":           (200, 200, 50),
    "Bartack":                 (200, 50, 200),
    "Open Seam":               (50, 200, 200),
    "Mismatch pattern":        (90, 90, 220),
    "Hole/gash":               (220, 90, 90),
    "Pleated":                 (90, 220, 90),
    "Uneven Seam":             (180, 180, 150),
    "Missing component":       (150, 180, 180),
    "Damaged":                 (180, 150, 180),
    "Fabric defect":           (160, 40, 80),
    "Shading":                 (40, 160, 80),
    "Others":                  (80, 40, 160),
}

def get_color(defect_type):
    return defect_colors.get(defect_type, (128, 128, 128))

for root, dirs, files in os.walk(base_folder):
    if root == base_folder:
        continue
    rel_path = os.path.relpath(root, base_folder)
    out_dir = os.path.join(output_base_folder, rel_path)
    os.makedirs(out_dir, exist_ok=True)

    for yaml_file in glob.glob(os.path.join(root, "*_defects.yaml")):
        print(yaml_file)
        with open(yaml_file, "r") as f:
            data = yaml.safe_load(f)

        image_name = os.path.basename(data["image"])
        img_path = os.path.join(root, image_name)
        if not os.path.exists(img_path):
            continue

        img = cv2.imread(img_path)
        if img is None:
            continue

        h, w = img.shape[:2]
        disp_w, disp_h = data.get("display_size", (1, 1))

        for d in data.get("defects", []):
            x_disp, y_disp = d["coords_display"]
            x_real = int(x_disp / disp_w * w)
            y_real = int(y_disp / disp_h * h)
            color = get_color(d["defect_type"])
            half_side = 100
            x1, y1 = x_real - half_side, y_real - half_side
            x2, y2 = x_real + half_side, y_real + half_side
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 3)
            cv2.putText(img, d["defect_type"], (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)

        out_path = os.path.join(out_dir, image_name)
        cv2.imwrite(out_path, img)
