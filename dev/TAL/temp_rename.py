#!/usr/bin/env python3

import os
import json
from pathlib import Path
import re

# Define folder and prefix
folder = Path("/mnt/datasets/tal/garment/database/all_aug/all_focus/")
prefix = "augment3_part_3_v3_"
pattern = re.compile(r"^\d{17}_camera\.jpg$")

# Process all files in the folder
for file in folder.iterdir():
    if file.suffix.lower() == ".jpg" and pattern.match(file.name):
        # Build new name
        new_img_name = prefix + file.name
        new_img_path = file.with_name(new_img_name)

        # Rename image
        file.rename(new_img_path)
        print(f"Renamed image: {file.name} -> {new_img_name}")

        # Check for corresponding JSON
        json_file = file.with_suffix(".json")
        if json_file.exists():
            with open(json_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Update imagePath
            data["imagePath"] = new_img_name

            # Save with new JSON name
            new_json_name = prefix + json_file.name
            new_json_path = json_file.with_name(new_json_name)
            with open(new_json_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            json_file.unlink()  # remove old JSON
            print(f"Renamed annotation: {json_file.name} -> {new_json_name}")

        else:
            print(f"Warning: No annotation for {file.name}")
