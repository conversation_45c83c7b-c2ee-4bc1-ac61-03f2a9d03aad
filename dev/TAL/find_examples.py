#!/usr/bin/env python3
import os
import json
import shutil
import random
import argparse

def index_vis_images(vis_folder):
    vis_dict = {}
    for root, dirs, files in os.walk(vis_folder):
        for fname in files:
            if fname.lower().endswith(".jpg"):
                vis_dict[fname] = os.path.join(root, fname)
    return vis_dict

def collect_existing_basenames(existing_folder):
    existing = set()
    for root, dirs, files in os.walk(existing_folder):
        for fname in files:
            if fname.lower().endswith(".jpg") and not fname.endswith("_vis.jpg"):
                base = os.path.splitext(fname)[0]
                existing.add(base)
    return existing

def main():
    parser = argparse.ArgumentParser(description="Select examples and copy images + their visualized counterparts.")
    parser.add_argument("--input_folder", required=True, help="Folder with JSON + original images")
    parser.add_argument("--output_folder", required=True, help="Destination folder for selected images + vis images")
    parser.add_argument("--vis_folder", required=True, help="Folder containing nested subfolders with visualized images")
    parser.add_argument("--existing_examples_folder", default=None, help="Optional folder with subfolders of already selected examples to exclude")
    parser.add_argument("--num_select", type=int, default=300, help="Total number of examples to select (default: 300)")
    args = parser.parse_args()

    target_classes = [
        "run_off_stitches", "broken_stitches", "skip_stitches",
        "puckering-fullness", "bartack", "open_seam",
        "uneven_seam", "fabric_defect"
    ]
    class_files = {c: [] for c in target_classes}

    vis_dict = index_vis_images(args.vis_folder)
    already_selected = collect_existing_basenames(args.existing_examples_folder) if args.existing_examples_folder else set()

    for fname in os.listdir(args.input_folder):
        if not fname.lower().endswith(".json"):
            continue
        path_json = os.path.join(args.input_folder, fname)

        with open(path_json, "r") as f:
            data = json.load(f)
        labels = [s["label"] for s in data.get("shapes", [])]

        for c in target_classes:
            if c in labels:
                base_name = os.path.splitext(fname)[0]
                img_name = base_name + ".jpg"
                src_img = os.path.join(args.input_folder, img_name)

                if os.path.exists(src_img) and (img_name in vis_dict) and (base_name not in already_selected):
                    class_files[c].append(fname)
                break

    quota_per_class = args.num_select // len(target_classes)
    selected_files = []

    for c in target_classes:
        random.shuffle(class_files[c])
        selected_files += class_files[c][:quota_per_class]

    selected_files = list(set(selected_files))
    os.makedirs(args.output_folder, exist_ok=True)

    for json_name in selected_files:
        base_name = os.path.splitext(json_name)[0]
        img_name = base_name + ".jpg"
        src_img = os.path.join(args.input_folder, img_name)

        if not os.path.exists(src_img) or (img_name not in vis_dict):
            continue

        dst_img = os.path.join(args.output_folder, img_name)
        shutil.copy(src_img, dst_img)

        src_vis = vis_dict[img_name]
        dst_vis = os.path.join(args.output_folder, f"{base_name}_vis.jpg")
        shutil.copy(src_vis, dst_vis)

if __name__ == "__main__":
    main()
