import numpy as np
import torch
from glob import glob
from PIL import Image

print("Computing class weights")

# ✏️ Adjust the pattern to your dataset layout if needed
MASKS = glob('/mnt/data/datasets/medical/database/split_2025-07-09/train/seg/*.png')

NUM_CLASSES = 15                                   # 22 defects + background
pixel_counts = np.zeros(NUM_CLASSES, dtype=np.int64)

for idx, path in enumerate(MASKS, 1):
    mask = np.array(Image.open(path), dtype=np.int32)
    for cls in range(NUM_CLASSES):
        pixel_counts[cls] += np.sum(mask == cls)
    print(f"{idx}/{len(MASKS)}")

# Relative frequency of each class
freq = pixel_counts / pixel_counts.sum()

# Median of the frequencies that are > 0  (classes actually present)
non_zero = freq > 0
median = np.median(freq[non_zero])

# Median-frequency balancing, but zero-out absent classes
weights = np.zeros_like(freq, dtype=np.float32)
weights[non_zero] = median / freq[non_zero]

# Optional cap for the damage class (index 7)
#weights[7] = min(weights[7], 8.0)

# Save for PyTorch
torch.save(torch.tensor(weights, dtype=torch.float32),
           '/mnt/data/projects/hrnet/configs/medical/class_weights.pt')

print("Class weights:", weights)
