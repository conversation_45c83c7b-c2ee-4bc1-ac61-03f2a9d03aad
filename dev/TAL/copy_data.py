import os
import shutil

# Define base paths
splits = ['train', 'val', 'test']
base_src = '/mnt/datasets/tal/garment/database/batch6_cvat_focus_windows/split'
base_dst = '/mnt/datasets/tal/garment/database/all_focus_windows/split/'

for split in splits:
    src_images = os.path.join(base_src, split, 'images')
    src_seg = os.path.join(base_src, split, 'seg')
    
    dst_images = os.path.join(base_dst, "train", 'images')
    dst_seg = os.path.join(base_dst, "train", 'seg')
    #dst_images = os.path.join(base_dst, split, 'images')
    #dst_seg = os.path.join(base_dst, split, 'seg')

    # Create destination directories if they don't exist
    os.makedirs(dst_images, exist_ok=True)
    os.makedirs(dst_seg, exist_ok=True)

    # Copy image files
    for fname in os.listdir(src_images):
        shutil.copy2(os.path.join(src_images, fname), dst_images)

    # Copy segmentation files
    for fname in os.listdir(src_seg):
        shutil.copy2(os.path.join(src_seg, fname), dst_seg)

print("Copying completed successfully.")
