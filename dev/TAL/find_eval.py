import re

def parse_log_file(file_path):
    best_metrics = {}  # Store best values per class
    current_epoch = None
    inside_iou_section = False  # Flag to track when we're inside an IoU table

    # Regular expressions
    epoch_re = re.compile(r"\[epoch (\d+)\]")  # Matches [epoch 85]
    
    # Adjusted regex to match the IoU table format precisely
    iou_re = re.compile(
        r"^\s*(\d+)\s+([\w-]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)\s+([\d.]+)"
    )

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            # Check if this line contains epoch information
            epoch_match = epoch_re.search(line)
            if epoch_match:
                current_epoch = int(epoch_match.group(1))

            # Detect start of IoU section
            if "IoU:" in line:
                inside_iou_section = True
                continue

            # Parse IoU table
            if inside_iou_section:
                iou_match = iou_re.match(line)
                if iou_match:
                    class_id = int(iou_match.group(1))
                    label = iou_match.group(2)
                    iU = float(iou_match.group(3))
                    precision = float(iou_match.group(7))
                    recall = float(iou_match.group(8))

                    # If this class has a better IoU than the previous best, update it
                    if class_id not in best_metrics or iU > best_metrics[class_id]["iU"]:
                        best_metrics[class_id] = {
                            "label": label,
                            "epoch": current_epoch,
                            "iU": iU,
                            "precision": precision,
                            "recall": recall,
                        }

            # Stop parsing if we reach a summary section (like "Mean" or "Best Epochs")
            if "Mean:" in line or "Test:" in line:
                inside_iou_section = False

    return best_metrics

def main():
    file_path = "/mnt/data/projects/hrnet/logs/2025-04-14_tal_windows_1.log"  # Update the path
    best_metrics = parse_log_file(file_path)

    print(f"{'Id':<5} {'Label':<25} {'Best Epoch':<12} {'Best iU':<10} {'Precision':<10} {'Recall':<10}")
    print("-" * 70)
    for class_id, metrics in sorted(best_metrics.items()):
        print(f"{class_id:<5} {metrics['label']:<25} {metrics['epoch']:<12} {metrics['iU']:<10.2f} {metrics['precision']:<10.2f} {metrics['recall']:<10.2f}")

if __name__ == "__main__":
    main()
