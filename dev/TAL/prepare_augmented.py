#!/usr/bin/env python3
"""
Clean a Photoshop‑augmented LabelMe dataset so that it only contains the original camera
images and their matching annotation files.

Features
========
* Copies *only* the original images (pattern: ``<TIMESTAMP>_camera.jpg``) to the output folder.
* Ignores helper images such as ``*_vis.jpg``, ``* cheat sheet.jpg``, ``* cheatsheet.jpg``,
  ``* cs.jpg`` (case‑insensitive).
* Updates any annotation JSON that was created on a helper image so that both the
  ``imagePath`` and, if present, ``filename`` fields point to the original image.
* Optionally strips the (usually huge) base‑64‐encoded ``imageData`` blob to save space.
* Writes the cleaned JSON and its corresponding original image **only if both exist**.

Usage
-----
Run from the command line (Python ⩾ 3.8):

```bash
python clean_labelme_dataset.py \
  --input  /mnt/datasets/tal/garment/raw/json/batch_aug2/augmented_back_1/augmented_back_1 \
  --output /mnt/datasets/tal/garment/raw/json/batch_aug2/augmented_back_1_cleaned
```

You may add ``--keep-image-data`` to **retain** the base‑64 image blobs inside the JSON
files.

```text
input  ─┬─ 20241219102705126_camera.jpg      (kept)
        ├─ 20241219102705126_camera_vis.jpg  (ignored)
        ├─ 20241219104122234_camera cs.jpg   (ignored)
        └─ 20241219102705126_camera_vis.json (→ updated & renamed to …_camera.json)

output ─┬─ 20241219102705126_camera.jpg
        └─ 20241219102705126_camera.json     (cleaned)
```
"""
from __future__ import annotations

import argparse
import json
import os
import re
import shutil
from pathlib import Path
from typing import Dict, Iterable

# ---------------------------------------------------------------------------
# Regex setup
# ---------------------------------------------------------------------------
# Matches the *original* image exactly:   20241219102705126_camera.jpg
ORIGINAL_PATTERN = re.compile(r"^(\d+_camera)\.jpg$", re.IGNORECASE)

# Matches *any* variation (original, vis, cheat‑sheet, cs …) so we can extract the UID.
BASE_PATTERN = re.compile(r"^(\d+_camera)(?:.*)\.jpg$", re.IGNORECASE)

# Any helper suffix that marks an image as *non‑original*.
HELPER_SUFFIX = re.compile(r"(_vis\b|\svis\b|cheatsheet|cheat\s*sheet|\bcs\b)", re.IGNORECASE)


# ---------------------------------------------------------------------------
# Utility helpers
# ---------------------------------------------------------------------------

def is_original(img_name: str) -> bool:
    """Return *True* if *img_name* is an original camera JPEG."""
    return bool(ORIGINAL_PATTERN.fullmatch(img_name))


def base_id(img_name: str) -> str | None:
    """Return the UID (**<TIMESTAMP>_camera**) or *None* if it cannot be parsed."""
    m = BASE_PATTERN.match(img_name)
    return m.group(1) if m else None


def collect_original_images(src: Path) -> Dict[str, Path]:
    """Return a mapping *UID -> Path* for every original image in *src*."""
    originals: Dict[str, Path] = {}
    for img in src.glob("*.jp*g"):
        if is_original(img.name):
            uid = base_id(img.name)
            if uid:
                originals[uid] = img
    return originals


# ---------------------------------------------------------------------------
# Core routine
# ---------------------------------------------------------------------------

def clean_dataset(input_dir: Path, output_dir: Path, keep_image_data: bool = False) -> None:
    originals = collect_original_images(input_dir)
    if not originals:
        raise SystemExit(f"No original images found inside {input_dir!s}.")

    output_dir.mkdir(parents=True, exist_ok=True)

    # Process every JSON file we can find.
    for ann_file in input_dir.glob("*.json"):
        # Read the annotation first. If it is malformed we just skip it.
        try:
            with ann_file.open() as fh:
                anno = json.load(fh)
        except Exception as exc:  # noqa: BLE001, PERF203
            print(f"⚠️  {ann_file.name}: cannot parse JSON (ignored) – {exc}")
            continue

        # Heuristically detect which image this annotation is meant for.
        img_path_in_json = anno.get("imagePath") or anno.get("filename") or ""
        uid = base_id(Path(img_path_in_json).name) or base_id(ann_file.stem)
        if not uid:
            print(f"⚠️  {ann_file.name}: cannot derive UID – skipped")
            continue

        # Do we have the original image on disk?
        original_img: Path | None = originals.get(uid)
        if original_img is None:
            # Maybe someone deleted the camera photo but kept helper pics – skip pair.
            print(f"⚠️  {uid}: original image missing – pair skipped")
            continue

        original_img_name = original_img.name  # e.g. 20241219102705126_camera.jpg

        # Update annotation fields.
        anno["imagePath"] = original_img_name
        if "filename" in anno:
            anno["filename"] = original_img_name
        if not keep_image_data and "imageData" in anno:
            anno.pop("imageData")

        # Target paths.
        dst_json = output_dir / f"{uid}.json"
        dst_img = output_dir / original_img_name

        # Write JSON (indent for readability, keep keys stable).
        with dst_json.open("w") as fh:
            json.dump(anno, fh, indent=2, ensure_ascii=False)

        # Copy image once per UID.
        if not dst_img.exists():
            shutil.copy2(original_img, dst_img)

    # -------------------------------------------------------------------
    # Print a short summary for the user.
    # -------------------------------------------------------------------
    copied = len(list(output_dir.glob("*.jp*g")))
    anns   = len(list(output_dir.glob("*.json")))
    print(f"✅  Done. {copied} image(s) and {anns} annotation(s) written to {output_dir}.")


# ---------------------------------------------------------------------------
# Command‑line interface
# ---------------------------------------------------------------------------

def parse_cli(argv: Iterable[str] | None = None) -> argparse.Namespace:  # noqa: D401
    """Return the CLI arguments."""
    parser = argparse.ArgumentParser(description="Clean a LabelMe garment dataset")
    parser.add_argument("--input", required=True, type=Path, help="Folder with the augmented dataset")
    parser.add_argument("--output", required=True, type=Path, help="Destination folder for the cleaned pair files")
    parser.add_argument(
        "--keep-image-data",
        action="store_true",
        help="Keep the base64‑encoded imageData blob inside the JSON (default: strip it)",
    )
    return parser.parse_args(argv)


def main() -> None:  # noqa: D401
    args = parse_cli()
    clean_dataset(args.input, args.output, keep_image_data=args.keep_image_data)


if __name__ == "__main__":
    main()
