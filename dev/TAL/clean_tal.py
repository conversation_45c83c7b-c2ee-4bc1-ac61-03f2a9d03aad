#!/usr/bin/env python3

import argparse
import os
import json
import re

# Dictionary to normalize variant/misspelled labels
# Dictionary to normalize variant/misspelled labels
CANONICAL_LABELS = {
    # Canonical labels (all lowercase and hyphen/underscore normalized)
    "thread end / fly": "thread end/fly thread",
    "thread end / fly thread": "thread end/fly thread",
    "thread end/fly thread": "thread end/fly thread",
    "thread end": "thread end/fly thread",
    "threaed end / fly thread": "thread end/fly thread",
    "threaed_end___fly_thread": "thread end/fly thread",
    "thread_end__fly_thread": "thread end/fly thread",
    "thread_end_fly_thread": "thread end/fly thread",
    "threadend___fly_thread": "thread end/fly thread",

    "run off stitches": "run off stitches",
    "run_off_stitches": "run off stitches",

    "missing component": "missing component",
    "missin component": "missing component",
    "missing-component": "missing component",
    "missing_component": "missing component",
    "_missing_component": "missing component",
    "missing componet": "missing component",
    "missing_componet": "missing component",

    "open seam": "open seam",
    "opeam seam": "open seam",
    "open_seam": "open seam",

    "mismatch pattern": "mismatch pattern",
    "mismatch patterm": "mismatch pattern",
    "mismatch patern": "mismatch pattern",
    "mismatch patter": "mismatch pattern",
    "mismatch_pattern": "mismatch pattern",
    "mismatch_-_pattern": "mismatch pattern",
    "mismatch_pattern__": "mismatch pattern",

    "dirty - soil - stain": "dirty-soil-stain",
    "dirty- soil- stain": "dirty-soil-stain",
    "dirty-soil-stain": "dirty-soil-stain",
    "dirty_soil_stain": "dirty-soil-stain",
    "dirty-soil-stai": "dirty-soil-stain",
    "dyrty-soil-stain": "dirty-soil-stain",
    "disrty-soil-stain": "dirty-soil-stain",
    "dirthy_-_soil_-_stain": "dirty-soil-stain",
    "dirty_-soil_-stain": "dirty-soil-stain",

    "damaged": "damage",
    "damage": "damage",
    "damagen": "damage",
    "d": "damage",

    "shading": "shading",

    "uneven seam": "uneven seam",
    "uneven_seam": "uneven seam",
    "uneven_sean": "uneven seam",

    "poor shape": "poor shape",
    "poor_shape": "poor shape",
    "poor_shaoe": "poor shape",
    "ploor shape": "poor shape",

    "bartack": "bartack",
    "bartack ": "bartack",

    "broken stitches": "broken stitches",
    "broken": "broken stitches",
    "br": "broken stitches",
    "broken_stitches": "broken stitches",

    "placket": "placket",
    "Placket": "placket",

    "puckering-fullness": "puckering-fullness",
    "puckering": "puckering-fullness",
    "puckering - fullness": "puckering-fullness",
    "puckering- fullness": "puckering-fullness",
    "puckering_-_fullnes": "puckering-fullness",
    "puckering_-fullness": "puckering-fullness",
    "puckering___fullness": "puckering-fullness",
    "puckering_fullness": "puckering-fullness",
    "puckering – fullness": "puckering-fullness",
    "puckering -fullness": "puckering-fullness",
    "puckering - fullnes": "puckering-fullness",
    "puckering -fullnes": "puckering-fullness",
    "puckering fullness": "puckering-fullness",

    "skip stitches": "skip stitches",
    "skipstitches": "skip stitches",
    "skip-stitches": "skip stitches",
    "skipped_stitches": "skip stitches",
    "skipped stitches": "skip stitches",

    "pleated": "pleated",

    "hole / gash": "hole/gash",
    "hole/ gash": "hole/gash",
    "hole/gash": "hole/gash",
    "hole /gash": "hole/gash",
    "hole_gash": "hole/gash",

    "fabric defect": "fabric defect",
    "fabric_defect": "fabric defect",
    "fabric_defect__": "fabric defect",
    "fabric defecrt": "fabric defect",

    "twisted roping": "twisted roping",
    "twisted_roping": "twisted roping",
    "twisted_roping__": "twisted roping",
    "twuisted roping": "twisted roping",

    "button (hole)": "button (hole)",
    "button__hole_": "button (hole)",
    "botton (hole)": "button (hole)",
    "buttom (hole)": "button (hole)",

    "others": "others",
    "other": "others",
    "Others": "others",
    "thread end/fly threqd": "thread end/fly thread",

    # Canonical values as-is for completeness
    "thread end/fly thread": "thread end/fly thread",
    "run off stitches": "run off stitches",
    "missing component": "missing component",
    "open seam": "open seam",
    "mismatch pattern": "mismatch pattern",
    "dirty-soil-stain": "dirty-soil-stain",
    "damage": "damage",
    "shading": "shading",
    "uneven seam": "uneven seam",
    "poor shape": "poor shape",
    "bartack": "bartack",
    "broken stitches": "broken stitches",
    "placket": "placket",
    "puckering-fullness": "puckering-fullness",
    "skip stitches": "skip stitches",
    "pleated": "pleated",
    "hole/gash": "hole/gash",
    "fabric defect": "fabric defect",
    "twisted roping": "twisted roping",
    "button (hole)": "button (hole)",
    "others": "others",
    "others}": "others",
    "fabric  defect": "fabric defect",
    "misssing componet": "missing component",
    "thread end / fly / thread": "thread end/fly thread",
    "thread end/ fly thread": "thread end/fly thread",
    "dmaged": "damage"
}






def sanitize_label(label):
    """
    Convert label to lowercase and replace any character that is not a letter,
    digit, underscore, or hyphen with an underscore.
    """
    label = label.lower()
    return re.sub(r"[^a-z0-9_-]", "_", label)

def main():
    parser = argparse.ArgumentParser(description="Clean annotation JSON files.")
    parser.add_argument("input_path", help="Directory containing the original JSON files.")
    parser.add_argument("output_path", help="Directory to write cleaned JSON files.")
    args = parser.parse_args()

    for root, _, files in os.walk(args.input_path):
        for file_name in files:
            if file_name.lower().endswith(".json"):
                input_file = os.path.join(root, file_name)
                relative_path = os.path.relpath(input_file, args.input_path)
                output_file = os.path.join(args.output_path, relative_path)
                os.makedirs(os.path.dirname(output_file), exist_ok=True)

                try:
                    with open(input_file, "r", encoding="utf-8") as f:
                        data = json.load(f)
                except Exception as e:
                    print(f"Error reading JSON {input_file}: {e}")
                    continue

                data["imageData"] = None

                cleaned_shapes = []
                for shape in data.get("shapes", []):
                    label = shape.get("label", "").strip().lower()
                    canonical = CANONICAL_LABELS.get(label, label)
                    canonical_clean = sanitize_label(canonical)

                    if canonical_clean == "others":
                        continue  # Skip this shape entirely

                    shape["label"] = canonical_clean
                    cleaned_shapes.append(shape)

                data["shapes"] = cleaned_shapes
                    

                try:
                    with open(output_file, "w", encoding="utf-8") as out_f:
                        json.dump(data, out_f, ensure_ascii=False, indent=2)
                    print(f"Cleaned file written: {output_file}")
                except Exception as e:
                    print(f"Error writing cleaned JSON {output_file}: {e}")

if __name__ == "__main__":
    main()
