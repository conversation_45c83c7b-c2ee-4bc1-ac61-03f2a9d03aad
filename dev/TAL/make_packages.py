import shutil
import zipfile
from pathlib import Path

# Paths
source_folder  = Path("/mnt/datasets/tal/garment/raw/vis_batch9")
target_folder  = Path("/mnt/datasets/tal/garment/packages/dist/batch_9")
zip_filename   = "TAL_garment_batch_9_all_images.zip"          # final archive name
zip_path       = target_folder / zip_filename

# Prepare destination
target_folder.mkdir(parents=True, exist_ok=True)

# 1️⃣  Gather every *.jpg exactly two levels below source_folder
image_files = [
    img
    for outer in source_folder.iterdir() if outer.is_dir()
    for inner in outer.iterdir()          if inner.is_dir()
    for img   in inner.glob("*.jpg")
]

print(f"Found {len(image_files)} images")

# 2️⃣  Copy them into target_folder (flattened).
#     If two images share the same file name, prepend the inner-folder name
#     to keep them unique.
for img_path in image_files:
    dest = target_folder / img_path.name
    if dest.exists():                                      # name clash
        # create a unique name: "<innerfolder>_<originalname>.jpg"
        dest = target_folder / f"{img_path.parent.name}_{img_path.name}"
    shutil.copy2(img_path, dest)

print("All images copied to target_folder")

# 3️⃣  Zip everything in target_folder (excluding the zip itself)
with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zf:
    for file in target_folder.glob("*.jpg"):               # just the images
        zf.write(file, arcname=file.name)

print(f"Created archive: {zip_path}")
