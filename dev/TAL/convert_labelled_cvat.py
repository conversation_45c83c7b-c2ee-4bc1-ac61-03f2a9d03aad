#!/usr/bin/env python3

import argparse
import json
import xml.etree.ElementTree as ET
from pathlib import Path


def parse_labelme_style_xml(xml_path: Path) -> dict:
    tree = ET.parse(xml_path)
    root = tree.getroot()

    filename = root.findtext("filename", default="")
    image_width = int(root.findtext("imagesize/ncols", default="0"))
    image_height = int(root.findtext("imagesize/nrows", default="0"))

    result = {
        "version": "4.5.7",
        "flags": {},
        "shapes": [],
        "imagePath": filename,
        "imageData": None,
        "imageHeight": image_height,
        "imageWidth": image_width,
    }

    for obj in root.findall("object"):
        label = obj.findtext("name", default="undefined")

        polygon = obj.find("polygon")
        if polygon is not None:
            points = []
            for pt in polygon.findall("pt"):
                x = float(pt.findtext("x"))
                y = float(pt.findtext("y"))
                points.append([x, y])

            shape = {
                "label": label,
                "points": points,
                "group_id": None,
                "shape_type": "polygon",
                "flags": {},
            }
            result["shapes"].append(shape)

    return result


def convert_all_xmls(input_folder: Path, output_folder: Path):
    xml_files = list(input_folder.rglob("*.xml"))
    output_folder.mkdir(parents=True, exist_ok=True)
    print(f"Found {len(xml_files)} XML files to convert.")

    for xml_file in xml_files:
        try:
            json_data = parse_labelme_style_xml(xml_file)
            json_path = output_folder / (xml_file.stem + ".json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(json_data, f, indent=2)
        except Exception as e:
            print(f"[ERROR] Failed to convert {xml_file}: {e}")


def main():
    parser = argparse.ArgumentParser(description="Convert Datumaro-style LabelMe XMLs to JSON.")
    parser.add_argument("--input", required=True, type=Path, help="Input folder with nested XMLs.")
    parser.add_argument("--output", required=True, type=Path, help="Output folder for JSONs.")
    args = parser.parse_args()

    convert_all_xmls(args.input, args.output)


if __name__ == "__main__":
    main()
