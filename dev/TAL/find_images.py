#!/usr/bin/env python3

import argparse
import os
import shutil

def main():
    parser = argparse.ArgumentParser(description="Copy only images that have corresponding JSON files into one output directory (flat).")
    parser.add_argument('--json_path', required=True, help='Path containing subfolders (possibly nested) with JSON files.')
    parser.add_argument('--image_path', required=True, help='Path containing subfolders with images (recursively searched).')
    parser.add_argument('--output_path', required=True, help='Destination path where all files will be copied (flat).')
    args = parser.parse_args()

    os.makedirs(args.output_path, exist_ok=True)

    # Collect valid JSON filenames (without extension)
    json_files = set()
    for root, _, files in os.walk(args.json_path):
        for filename in files:
            if filename.lower().endswith('.json'):
                name_without_ext = os.path.splitext(filename)[0]
                json_files.add(name_without_ext)
                src_file = os.path.join(root, filename)
                shutil.copy2(src_file, os.path.join(args.output_path, filename))
    print("Finished collecting JSON files.")

    # Recursively walk through image_path to find matching image files
    copied_count = 0
    for root, _, files in os.walk(args.image_path):
        for filename in files:
            name, ext = os.path.splitext(filename)
            if name in json_files and ext.lower() in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}:
                src_image_path = os.path.join(root, filename)
                dst_image_path = os.path.join(args.output_path, filename)
                shutil.copy2(src_image_path, dst_image_path)
                copied_count += 1

    print(f"Copied {copied_count} matching image files into: {args.output_path}")

if __name__ == "__main__":
    main()
