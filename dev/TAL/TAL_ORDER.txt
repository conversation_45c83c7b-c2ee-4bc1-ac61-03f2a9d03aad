# Adding to Training

1. python dev/TAL/find_images.py --json_path /mnt/datasets/tal/garment/raw/json/batch6_cvat/json_format/ --image_path /mnt/datasets/tal/garment/raw/images/batch_7/ --output_path /mnt/datasets/tal/garment/database/batch6_cvat/
match novis images to the json files

2. python dev/TAL/clean_tal2.py 
find all labels dreamt up by the labelling team. add missing cases to clean_tal.py

3. python dev/TAL/clean_tal.py /mnt/datasets/tal/garment/database/batch3/ /mnt/datasets/tal/garment/database/batch3

4. python dev/create_sliding_windows.py --input_folder /mnt/datasets/tal/garment/database/batch --output_folder /mnt/datasets/tal/garment/database/patches_batch7 --window_size 512 --overlap 50 --bg_percentage 0.05

5. PYTHONPATH="." nohup python -u utils/prepare_data.py /mnt/datasets/tal/garment/database/batch6_cvat_focus_windows --classmap configs/tal/classmap_focus.yaml --labelme_dir all > logs/2025-06-11_prepare.log &

6. python utils/splitset.py /mnt/datasets/tal/garment/database/all_focus_windows/all/ /mnt/datasets/tal/garment/database/all_focus_windows/seg /mnt/datasets/tal/garment/database/all_focus_windows/split

7. python dev/TAL/copy_data.py 

# Creating packages

1. python dev/TAL/data_vis.py
2. python dev/TAL/make_packages.py 