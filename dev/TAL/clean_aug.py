#!/usr/bin/env python3
"""
clean_garment_dataset.py
========================

Enhanced cleaning utility for a mixed‑quality LabelMe dataset.

Improvements over the previous version
--------------------------------------
* **Handles annotation files ending in `.jpg.json`.**
* Works **annotation‑first**: every JSON drives the copy, guaranteeing images without annotations are dropped.
* Fully normalises filenames (e.g. `top.jpg.png` → `top.jpg`; annotation becomes `top.json`).
* Keeps a mirror of the source directory structure in the destination.

Usage (unchanged)
-----------------
```bash
python clean_garment_dataset.py \
  "/mnt/datasets/tal/garment/raw/json/batch_aug1/HiDrive-IMAGES_garment_augmentation_2 (1)/HiDrive-IMAGES_garment_augmentation_2" \
  "/mnt/datasets/tal/garment/raw/json/batch_aug1/HiDrive-IMAGES_garment_augmentation_2 (1)/HiDrive-IMAGES_garment_augmentation_cleaned"
```

Requirements
------------
```bash
pip install pillow
```
"""

from __future__ import annotations

import argparse
import base64
import json
import os
from pathlib import Path

from PIL import Image

# ---------------------------------------------------------------------------
# Configuration
# ---------------------------------------------------------------------------
IMAGE_EXTS = {".png", ".jpg", ".jpeg", ".PNG", ".JPG", ".JPEG"}
DOUBLE_EXTS = {ext1 + ext2 for ext1 in (".jpg", ".jpeg") for ext2 in (".png", ".jpeg", ".jpg")}


# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def strip_all_image_ext(fname: str) -> str:
    """Remove *all* known image extensions from *fname* and return the bare stem."""
    base = fname
    changed = True
    while changed:
        changed = False
        for ext in IMAGE_EXTS:
            if base.lower().endswith(ext.lower()):
                base = base[: -len(ext)]
                changed = True
    return base


def normalised_jpg_name(original_name: str) -> str:
    """Return a clean `something.jpg` regardless of how messy *original_name* was."""
    return strip_all_image_ext(original_name) + ".jpg"


def convert_to_jpg(src: Path, dst: Path) -> None:
    dst.parent.mkdir(parents=True, exist_ok=True)
    with Image.open(src) as im:
        if im.mode != "RGB":
            im = im.convert("RGB")
        im.save(dst, format="JPEG", quality=95, optimize=True)


def write_json(dst_json: Path, data: dict, new_image_name: str, dst_img: Path) -> None:
    data["imagePath"] = new_image_name

    # Keep imageData in sync if present and non‑empty
    if data.get("imageData"):
        try:
            with open(dst_img, "rb") as f:
                data["imageData"] = base64.b64encode(f.read()).decode("utf-8")
        except Exception:
            data.pop("imageData", None)

    dst_json.parent.mkdir(parents=True, exist_ok=True)
    with open(dst_json, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


# ---------------------------------------------------------------------------
# Main logic
# ---------------------------------------------------------------------------

def clean_dataset(src_root: Path, dst_root: Path) -> None:
    if not src_root.exists():
        raise FileNotFoundError(src_root)
    dst_root.mkdir(parents=True, exist_ok=True)

    processed, skipped_no_img = 0, 0

    # Walk all JSON annotation files (including those with double extension)
    for json_path in src_root.rglob("*.json"):
        rel_dir = json_path.parent.relative_to(src_root)

        # Load JSON once to access imagePath (fallback to filename logic if absent)
        try:
            data = json.loads(json_path.read_text(encoding="utf-8"))
        except Exception as e:
            print(f"⚠️  Skipping unreadable JSON {json_path}: {e}")
            continue

        image_path_field = data.get("imagePath") or json_path.name[:-5]  # drop .json
        candidate_img_path = json_path.parent / image_path_field

        # If the referenced image doesn’t exist, attempt recovery using glob‑by‑stem
        if not candidate_img_path.exists():
            stem = strip_all_image_ext(image_path_field)
            matches = [p for p in json_path.parent.glob(f"{stem}*") if p.suffix in IMAGE_EXTS or p.name.endswith(tuple(DOUBLE_EXTS))]
            candidate_img_path = matches[0] if matches else None

        if not candidate_img_path or not candidate_img_path.exists():
            skipped_no_img += 1
            continue  # Annotation without an image → drop

        clean_img_name = normalised_jpg_name(candidate_img_path.name)
        clean_json_name = strip_all_image_ext(clean_img_name) + ".json"

        dst_img_path = dst_root / rel_dir / clean_img_name
        dst_json_path = dst_root / rel_dir / clean_json_name

        convert_to_jpg(candidate_img_path, dst_img_path)
        write_json(dst_json_path, data, clean_img_name, dst_img_path)
        processed += 1

    print(f"✅ Processed {processed} annotations. Dropped {skipped_no_img} without images.")


# ---------------------------------------------------------------------------
# CLI
# ---------------------------------------------------------------------------

def main() -> None:
    parser = argparse.ArgumentParser(description="Clean a garment dataset in LabelMe format.")
    parser.add_argument("src", type=Path, help="Source directory with raw images + JSON files")
    parser.add_argument("dst", type=Path, help="Destination directory for cleaned dataset")
    args = parser.parse_args()

    clean_dataset(args.src, args.dst)


if __name__ == "__main__":
    main()
