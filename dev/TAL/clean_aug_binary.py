#!/usr/bin/env python3

import os
import json
import cv2
import numpy as np
from pathlib import Path
from PIL import Image

# Define paths
input_folder = Path("/mnt/datasets/tal/garment/raw/json/batch_aug4/raw/HiDrive-MAURICIO_NUEVO_PAQUETE_augment_3/augment3_part_2/")
original_folder = Path("/mnt/datasets/tal/garment/database/all/")
output_folder = Path("/mnt/datasets/tal/garment/raw/json/batch_aug4/raw/new_packet_1/")
output_folder.mkdir(parents=True, exist_ok=True)

def get_first_label(json_path):
    """Extract the first label from the original annotation JSON file"""
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
        for shape in data.get('shapes', []):
            label = shape.get('label')
            if label:
                return label
    except Exception as e:
        print(f"Failed to load label from {json_path}: {e}")
    return None

def extract_polygons(mask):
    """Convert white regions in binary mask to polygon points"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    polygons = []
    for cnt in contours:
        if len(cnt) >= 3:
            polygon = cnt.squeeze().tolist()
            if isinstance(polygon[0], list):
                polygon = [pt for pt in polygon]
            polygons.append(polygon)
    return polygons

# Iterate over all binary masks
for mask_file in sorted(input_folder.glob("*_camera_vis.jpg-copia.png")):
    # Extract ID from filename
    id_part = mask_file.name.split("_camera_vis.jpg-copia.png")[0].split("_")[-1]

    # Find corresponding image and original annotation
    aug_image = next(input_folder.glob(f"*_{id_part}_camera.jpg.png"), None)
    original_image = original_folder / f"{id_part}_camera.jpg"
    original_json = original_folder / f"{id_part}_camera.json"

    if not aug_image or not original_json.exists():
        print(f"Missing image or JSON for {id_part}")
        continue

    # Load and process mask
    mask = cv2.imread(str(mask_file), cv2.IMREAD_GRAYSCALE)
    polygons = extract_polygons(mask)
    if not polygons:
        print(f"No polygons found in mask {mask_file}")
        continue

    # Get label from original annotation
    label = get_first_label(original_json)
    if not label:
        print(f"No valid label found in {original_json}")
        continue

    # Convert image and save as JPG
    out_image_path = output_folder / aug_image.name.replace(".jpg.png", ".jpg")
    img = Image.open(aug_image).convert("RGB")
    img.save(out_image_path)

    # Create labelme annotation
    annotation = {
        "version": "5.3.1",
        "flags": {},
        "shapes": [],
        "imagePath": out_image_path.name,
        "imageData": None,
        "imageHeight": img.height,
        "imageWidth": img.width
    }

    for poly in polygons:
        annotation["shapes"].append({
            "label": label,
            "points": poly,
            "group_id": None,
            "shape_type": "polygon",
            "flags": {}
        })

    # Save JSON
    out_json_path = out_image_path.with_suffix(".json")
    with open(out_json_path, 'w') as f:
        json.dump(annotation, f, indent=2)

    print(f"Processed {out_image_path.name}")
