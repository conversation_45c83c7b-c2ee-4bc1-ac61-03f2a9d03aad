import os
from pathlib import Path

# Define paths
source_dir = Path("/mnt/datasets/tal/garment/raw/json/batch_aug1/rest_cleaned")
reference_dir = Path("/mnt/datasets/tal/garment/raw/json/batch_aug1/HiDrive-IMAGES_garment_augmentation_cleaned")

# Get base filenames (without extensions) of reference files
reference_files = {f.stem for f in reference_dir.glob("*") if f.suffix in [".jpg", ".json"]}

# Iterate through source files and delete if base name exists in reference set
for f in source_dir.glob("*"):
    if f.stem in reference_files and f.suffix in [".jpg", ".json"]:
        print(f"Deleting {f}")
        f.unlink()
