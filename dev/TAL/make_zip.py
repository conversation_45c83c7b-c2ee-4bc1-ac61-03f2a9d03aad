import os
import zipfile

base_dir = "/mnt/datasets/tal/garment/packages/dist/batch_5"
zip_path = os.path.join(base_dir, "tal_packages.zip")
image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}

with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if os.path.splitext(file)[1].lower() in image_extensions:
                full_path = os.path.join(root, file)
                arcname = os.path.relpath(full_path, start=base_dir)
                zipf.write(full_path, arcname)

print(f"Created zip file at: {zip_path}")
