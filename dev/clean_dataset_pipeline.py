import os
import shutil
import tarfile
import zipfile
from collections import defaultdict


# Define local directory
ROOT_DIRECTORY = "/home/<USER>/Desktop/Work/database"
FOLDERS = [
    "02_to-label", "03_on-going", "04_labeled", "05_reviewed", "06_database", "08_returned"
]

# Issues Tracking
duplicates_in_folders = []
duplicates_across_pipeline = []
orphan_jsons = []
empty_dirs = []
temp_dirs = []
extracted_files = []

def traverse_directory(root_path):
    """Recursively traverse a local directory."""
    all_entries = []
    for dirpath, _, filenames in os.walk(root_path):
        for file in filenames:
            full_path = os.path.join(dirpath, file)
            all_entries.append((file, full_path))
    return all_entries

def identify_duplicates_in_folders():
    """Identify duplicate files within each folder and its subfolders individually."""
    for folder in FOLDERS:
        folder_path = os.path.join(ROOT_DIRECTORY, folder)
        files_seen = {}  # Dictionary of lists to store all occurrences
        entries = traverse_directory(folder_path)

        for file_name, file_path in entries:
            if file_name in files_seen:
                duplicates_in_folders.append(file_path)
            else:
                files_seen[file_name] = [file_path]  # Store as list for future improvements

def identify_duplicates_across_pipeline():
    """Identify duplicate files across dataset pipeline with strict priority order."""
    priority_order = ["06_database", "05_reviewed", "08_returned", "04_labeled", "03_on-going", "02_to-label"]
    file_map = {}

    # Collect all file occurrences across folders
    for folder in priority_order:
        folder_path = os.path.join(ROOT_DIRECTORY, folder)
        entries = traverse_directory(folder_path)

        for file_name, file_path in entries:
            if file_name not in file_map:
                file_map[file_name] = []
            file_map[file_name].append((folder, file_path))

    # Track which files should be kept based on priority order
    retained_files = {}  # Store first seen file path by file name

    for file_name, locations in file_map.items():
        # Sort occurrences strictly based on priority order
        locations.sort(key=lambda x: priority_order.index(x[0]))

        # Retain only the first occurrence in the highest-priority folder
        first_instance = locations[0][1]
        retained_files[file_name] = first_instance

        # Mark all other occurrences as duplicates
        for _, file_path in locations[1:]:  # Start from the second occurrence
            duplicates_across_pipeline.append(file_path)


def remove_empty_dirs():
    """Recursively remove all empty directories, including nested ones."""
    empty_found = True  # Flag to track if we find empty directories
    while empty_found:
        empty_found = False  # Reset before each pass
        for root, dirs, files in os.walk(ROOT_DIRECTORY, topdown=False):
            if not files and not dirs:  # If the directory contains no files or subdirectories
                try:
                    os.rmdir(root)
                    empty_dirs.append(root)
                    empty_found = True  # A directory was removed, so we should re-run the pass
                except Exception as e:
                    print(f"Error deleting empty directory {root}: {e}")

def execute_cleanup(files, message):
    """Delete specified files after user confirmation."""
    if not files:
        print(f"No {message} to delete.")
        return
    
    print(f"\n🗑️ {message} found: {len(files)}")
    confirm = input(f"Do you want to delete {message}? (yes/no): ").strip().lower()
    if confirm == "yes":
        for file in files:
            try:
                if os.path.isdir(file):
                    shutil.rmtree(file, ignore_errors=True)
                else:
                    os.remove(file)
            except Exception as e:
                print(f"Error deleting {file}: {e}")
        print(f"✔ {message} deleted.")
    else:
        print(f"Skipped deleting {message}.")

def find_orphan_jsons():
    """Identify orphan JSON files within their respective directories."""
    entries = traverse_directory(ROOT_DIRECTORY)

    # Organize files by directory
    files_by_dir = defaultdict(list)
    for file_name, file_path in entries:
        dir_path = os.path.dirname(file_path)
        files_by_dir[dir_path].append((file_name, file_path))

    orphan_jsons.clear()

    # Process each directory separately
    for dir_path, files in files_by_dir.items():
        # Collect image filenames without their extensions for the current directory
        image_files = {os.path.splitext(file_name)[0] for file_name, _ in files 
                       if file_name.lower().endswith((".jpg", ".jpeg", ".png"))}

        # Identify orphaned JSONs in the same directory
        for file_name, file_path in files:
            if file_name.lower().endswith(".json"):
                base_name = os.path.splitext(file_name)[0]
                if base_name not in image_files:
                    orphan_jsons.append(file_path)

def extract_archives():
    """Recursively extract compressed files."""
    extracted_any = True
    while extracted_any:
        extracted_any = False
        entries = traverse_directory(ROOT_DIRECTORY)
        for file_name, full_path in entries:
            if file_name.endswith(".zip"):
                print("Extracting", full_path)
                extract_zip(full_path)
                extracted_any = True
            elif file_name.endswith((".tar", ".tgz", ".tar.gz")):
                print("Extracting", full_path)
                extract_tar(full_path)
                extracted_any = True

def extract_zip(full_path):
    """Extract a ZIP file locally."""
    try:
        extract_dir = os.path.splitext(full_path)[0]
        os.makedirs(extract_dir, exist_ok=True)
        with zipfile.ZipFile(full_path, "r") as zip_ref:
            zip_ref.extractall(extract_dir)
        extracted_files.append(full_path)
    except Exception as e:
        print(f"Error extracting ZIP file {full_path}: {e}")
    finally:
        os.remove(full_path)

def extract_tar(full_path):
    """Extract a TAR file locally."""
    try:
        extract_dir = os.path.splitext(full_path)[0]
        os.makedirs(extract_dir, exist_ok=True)
        with tarfile.open(full_path, "r:*") as tar_ref:
            tar_ref.extractall(extract_dir)
        extracted_files.append(full_path)
        os.remove(full_path)
    except Exception as e:
        print(f"Error extracting TAR file {full_path}: {e}")
    finally:
        os.remove(full_path)

def print_report():
    """Print detected issues and request confirmation before deletion."""
    execute_cleanup(duplicates_in_folders, "duplicates in folders")
    execute_cleanup(orphan_jsons, "orphaned JSON files")
    execute_cleanup(temp_dirs, "temporary system directories")
    execute_cleanup(duplicates_across_pipeline, "duplicates across the pipeline")
    remove_empty_dirs()
    execute_cleanup(empty_dirs, "empty directories")


if __name__ == "__main__":
    try:
        print("Extracting Archives")
        extract_archives()
        print(f"📂 Extracted archives: {len(extracted_files)}")
        identify_duplicates_in_folders()
        identify_duplicates_across_pipeline()
        find_orphan_jsons()
        print_report()
    except Exception as e:
        print(f"Critical error occurred: {e}")
