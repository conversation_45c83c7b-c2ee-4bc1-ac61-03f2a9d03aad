#!/usr/bin/env python3
"""
Parallel version: crops HRNet images to the garment bounding box
and rewrites JSON annotations. Uses all available CPU cores and
shows a global tqdm progress‑bar.
"""

import argparse, json, os, sys
from pathlib import Path
from concurrent.futures import Process<PERSON>oolExecutor
from functools import partial

from PIL import Image
from tqdm import tqdm


# ------------------------------ label config ------------------------------
GARMENT_LABELS = {
    "pants", "jacket", "overall", "t-shirt", "vest", "polo",
    "apron", "shirt", "pullover",
}


# ------------------------------ helpers -----------------------------------
def bbox_from_points(pts):
    xs, ys = zip(*pts)
    return int(min(xs)), int(min(ys)), int(max(xs)), int(max(ys))


def shape_overlaps_bbox(shape_pts, x0, y0, x1, y1):
    return any(x0 <= x <= x1 and y0 <= y <= y1 for x, y in shape_pts)


def adjust_points(pts, x_shift, y_shift, new_w, new_h):
    out = []
    for x, y in pts:
        nx, ny = x - x_shift, y - y_shift
        nx = min(max(nx, 0), new_w - 1)
        ny = min(max(ny, 0), new_h - 1)
        out.append([nx, ny])
    return out


# ------------------------------ core worker -------------------------------
def process_file(job):
    """
    Worker function executed in separate process.
    *job* = (json_path, img_src_dir, img_dst_dir)
    """
    json_path, img_src, img_dst = job

    try:
        with json_path.open() as f:
            data = json.load(f)

        # find garment polygon
        garment_shape = next(
            (sh for sh in data["shapes"] if sh["label"] in GARMENT_LABELS), None
        )
        if garment_shape is None:
            # skip files without garment polygons
            print(f"[SKIP] {json_path}: no garment shape found")
            return

        x0, y0, x1, y1 = bbox_from_points(garment_shape["points"])

        # load & crop image
        img_path = img_src / data["imagePath"]
        with Image.open(img_path) as im:
            cropped = im.crop((x0, y0, x1, y1))
            new_w, new_h = cropped.size
            cropped.save(img_dst / img_path.name, quality=95)

        # ---------- filter & adjust shapes ----------
        kept = []
        for sh in data["shapes"]:
            if sh["label"] in GARMENT_LABELS:
                # always drop garment polygons in the output
                continue
            if shape_overlaps_bbox(sh["points"], x0, y0, x1, y1):
                sh_new = sh.copy()
                sh_new["points"] = adjust_points(sh["points"], x0, y0, new_w, new_h)
                kept.append(sh_new)

        data["shapes"] = kept
        data["imageHeight"] = new_h
        data["imageWidth"] = new_w

        with (img_dst / json_path.name).open("w") as f:
            json.dump(data, f, indent=4)

    except Exception as exc:
        # Print the error but let the pool continue
        print(f"[ERROR] {json_path}: {exc}", file=sys.stderr)


# ------------------------------ CLI wrapper -------------------------------
def main():
    parser = argparse.ArgumentParser(
        description="Parallel garment‑cropped defect dataset cloning tool."
    )
    parser.add_argument("--src", required=True, type=Path)
    parser.add_argument("--dst", required=True, type=Path)
    parser.add_argument(
        "--workers",
        type=int,
        default=os.cpu_count(),
        help="Number of parallel workers (default: all CPU cores)",
    )
    args = parser.parse_args()

    # Build job list (one entry per JSON file) and create dest dirs
    jobs = []
    for split in ("train", "val", "test"):
        img_src = args.src / split / "images"
        img_dst = args.dst / split / "images"
        img_dst.mkdir(parents=True, exist_ok=True)

        jobs.extend((json_path, img_src, img_dst) for json_path in img_src.glob("*.json"))

    if not jobs:
        print("No JSON files found – check dataset paths.", file=sys.stderr)
        sys.exit(1)

    # Execute in parallel with global tqdm progress‑bar
    with ProcessPoolExecutor(max_workers=args.workers) as pool:
        list(
            tqdm(
                pool.map(process_file, jobs, chunksize=32),
                total=len(jobs),
                desc="Processing images",
            )
        )

    print("✅  All splits finished.")


if __name__ == "__main__":
    main()
