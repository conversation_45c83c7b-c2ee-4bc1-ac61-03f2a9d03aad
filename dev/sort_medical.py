#!/usr/bin/env python3

import os
import shutil

def main():
    # Directory containing JSON files
    JSON_DIR = "/mnt/data/datasets/defis/anomaly-detection/sigvaris/results_stk_1_detections"
    
    # Directory containing sorted images
    IMAGE_DIR = "/home/<USER>/shared/projects/defis_sigvaris/04_software/database/results_stk_1_sorted"#"/mnt/data/datasets/defis/anomaly-detection/sigvaris/sorted_01"
    
    # Walk through IMAGE_DIR recursively
    for root, dirs, files in os.walk(IMAGE_DIR):
        for filename in files:
            if filename.lower().endswith('.jpg'):
                img_path = os.path.join(root, filename)
                img_basename = os.path.splitext(filename)[0]
                json_filename = img_basename + '.json'
                json_path = os.path.join(JSON_DIR, json_filename)
                
                if os.path.exists(json_path):
                    # Copy JSON file to the image's directory
                    try:
                        shutil.copy2(json_path, root)
                        print(f"Copied {json_filename} to {root}")
                    except Exception as e:
                        print(f"Error copying {json_filename} to {root}: {e}")
                else:
                    print(f"JSON file {json_filename} not found for image {img_path}")

if __name__ == "__main__":
    main()
