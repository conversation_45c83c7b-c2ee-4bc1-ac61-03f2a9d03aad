# To install pytorch manually run the following command:
# pip install torch==1.7.1+cu110 torchvision==0.8.2+cu110 torchaudio==0.7.2 -f https://download.pytorch.org/whl/torch_stable.html

argon2-cffi==20.1.0
async-generator==1.10
attrs==20.3.0
backcall==0.2.0
bleach==3.3.0
cffi==1.14.5
cmake==3.18.4.post1
coolname==1.1.0
cycler==0.10.0
Cython==0.29.21
dataclasses==0.6
decorator==4.4.2
defusedxml==0.6.0
distro==1.5.0
dominate==2.6.0
entrypoints==0.3
future==0.18.2
h5py==3.1.0
imageio==2.9.0
imantics==0.1.12
imgviz==1.4.1
ipykernel==5.4.3
ipython==7.20.0
ipython-genutils==0.2.0
ipywidgets==7.6.3
jedi==0.18.0
Jinja2==2.11.3
joblib==1.0.1
jsonschema==3.2.0
jupyter==1.0.0
jupyter-client==6.1.11
jupyter-console==6.2.0
jupyter-core==4.7.1
jupyterlab-pygments==0.1.2
jupyterlab-widgets==1.0.0
kiwisolver==1.3.1
labelme==4.5.13
lxml==4.6.2
MarkupSafe==1.1.1
matplotlib==3.2.2
mistune==0.8.4
nbclient==0.5.2
nbconvert==6.0.7
nbformat==5.1.2
nest-asyncio==1.5.1
networkx==2.5
notebook==6.2.0
numpy==1.20.1
opencv-python==********
packaging==20.9
pandocfilters==1.4.3
parso==0.8.1
pexpect==4.8.0
pickleshare==0.7.5
piexif==1.1.3
Pillow==8.1.0
prometheus-client==0.9.0
prompt-toolkit==3.0.16
protobuf==3.14.0
ptyprocess==0.7.0
pycparser==2.20
Pygments==2.8.0
pyparsing==2.4.7
PyQt5==5.15.6
PyQt5-Qt5==5.15.2
PyQt5-sip==12.9.0
pyrsistent==0.17.3
python-dateutil==2.8.1
python-polylabel==0.6
PyWavelets==1.1.1
PyYAML==5.4.1
pyzmq==22.0.3
qtconsole==5.0.2
QtPy==1.9.0
runx==0.0.6
scikit-build==0.11.1
scikit-image==0.18.1
scikit-learn==0.24.1
scipy==1.6.0
Send2Trash==1.5.0
six==1.15.0
sklearn==0.0
tabulate==0.8.7
tensorboardX==2.1
termcolor==1.1.0
terminado==0.9.2
testpath==0.4.4
threadpoolctl==2.1.0
tifffile==2021.2.1
#torch==1.7.1+cu110
#torchaudio==0.7.2
#torchvision==0.8.2+cu110
tornado==6.1
tqdm==4.56.2
traitlets==5.0.5
typing-extensions==*******
wcwidth==0.2.5
webencodings==0.5.1
widgetsnbextension==3.5.1
xmljson==0.2.1
