"""
Copyright 2020 Nvidia Corporation

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.


NIPS Dataset Loader
"""
import os
import json
import glob

from config import cfg, update_dataset_cfg
from runx.logx import logx
from datasets.base_loader import BaseLoader
from datasets.utils import make_dataset_folder
from datasets import uniform
import imgviz
import yaml


class Loader(BaseLoader):
    num_classes = None
    ignore_label = 255
    trainid_to_name = {}
    color_mapping = []

    def __init__(self, mode, quality='semantic', joint_transform_list=None,
                 img_transform=None, label_transform=None, eval_folder=None, class_map_path=None):

        super(Loader, self).__init__(quality=quality,
                                     mode=mode,
                                     joint_transform_list=joint_transform_list,
                                     img_transform=img_transform,
                                     label_transform=label_transform)
        self.class_map_path = class_map_path
        root = cfg.DATASET.NIPS_DIR
        self.fill_trainid_to_name()
        self.fill_colormap()
        print("Initiating NIPS Loader --- ")
        ######################################################################
        # Assemble image lists
        ######################################################################
        if mode == 'folder':
            self.all_imgs = make_dataset_folder(eval_folder)
        else:
            splits = {'train': 'train',
                      'val': 'val',
                      'test': 'test'}
            split_name = splits[mode]       
            img_ext = 'jpg'
            mask_ext = 'png'
            img_root = os.path.join(root, split_name, 'images')
            print("img_root", img_root)

            mask_root = os.path.join(root, split_name, 'seg')

            self.all_imgs = self.find_images(img_root, mask_root, img_ext,
                                             mask_ext)
        logx.msg('Loaded {} images'.format(len(self.all_imgs)))

        self.centroids = uniform.build_centroids(self.all_imgs,
                                                 self.num_classes,
                                                 self.train,
                                                 cv=cfg.DATASET.CV)
        self.build_epoch()


    def fill_colormap(self):
        color_map = imgviz.label_colormap()
        palette = []
        for i,color in enumerate(color_map):
            if i < len(self.trainid_to_name.keys()):
                key = [color[0],color[1],color[2]]
                palette.extend(key)
            else:
                break
        
        zero_pad = 256 * 3 - len(palette)
        for i in range(zero_pad):
            palette.append(0)
        self.color_mapping = palette
    
    def fill_trainid_to_name(self):
        with open(self.class_map_path, 'r') as file:
            class_map = yaml.safe_load(file)

        self.trainid_to_name = {}
        for idx, entry in enumerate(class_map):
            label = entry  # Extract the label (string part)
            self.trainid_to_name[idx] = label
        self.num_classes = len(self.trainid_to_name)
        update_dataset_cfg(num_classes=self.num_classes, ignore_label=self.ignore_label)


    def fill_colormap_and_names(self, config_fn):
        """
        Mapillary code for color map and class names

        Outputs
        -------
        self.trainid_to_name
        self.color_mapping
        """
        with open(config_fn) as config_file:
            config = json.load(config_file)
        config_labels = config['labels']

        # calculate label color mapping
        colormap = []
        self.trainid_to_name = {}
        for i in range(0, len(config_labels)):
            colormap = colormap + config_labels[i]['color']
            name = config_labels[i]['readable']
            name = name.replace(' ', '_')
            self.trainid_to_name[i] = name
        self.color_mapping = colormap
