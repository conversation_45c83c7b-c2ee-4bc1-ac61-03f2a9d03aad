"""
Copyright 2020 Nvidia Corporation

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

DEFECT Dataset Loader
"""
import os
import json
import glob

from config import cfg
from runx.logx import logx
from datasets.base_loader import BaseLoader
from datasets.utils import make_dataset_folder
from datasets import uniform
import imgviz
import numpy as np
from PIL import Image


class Loader(BaseLoader):
    num_classes = 22
    ignore_label = 255
    trainid_to_name = {}
    color_mapping = []

    def __init__(self, mode, quality='semantic', joint_transform_list=None,
                 img_transform=None, label_transform=None, eval_folder=None):
        super(Loader, self).__init__(quality=quality,
                                           mode=mode,
                                           joint_transform_list=joint_transform_list,
                                           img_transform=img_transform,
                                           label_transform=label_transform)
        root = cfg.DATASET.TAL_WINDOWS_DIR
        self.fill_trainid_to_name()
        self.fill_colormap()
        print("Initiating TAL Windows Loader --- ")

        if mode == 'folder':
            self.all_imgs = make_dataset_folder(eval_folder)
        else:
            splits = {'train': 'train',
                      'val': 'val',
                      'test': 'test'}
            split_name = splits[mode]
            img_ext = 'jpg'
            mask_ext = 'png'
            img_root = os.path.join(root, split_name, 'images')
            mask_root = os.path.join(root, split_name, 'seg')
            self.all_imgs = self.find_images(img_root, mask_root, img_ext, mask_ext)

        logx.msg('all imgs {}'.format(len(self.all_imgs)))
        self.centroids = uniform.build_centroids(self.all_imgs,
                                                 self.num_classes,
                                                 self.train,
                                                 cv=cfg.DATASET.CV)
        self.build_epoch()

    def __getitem__(self, index):
        """
        Generate data:

        :return:
        - image: image, tensor
        - mask: mask, tensor
        - image_name: basename of file, string
        """
        max_attempts = 10
        attempt = 0
        while attempt < max_attempts:
            # Pick an image, fill in defaults if not using class uniform
            if len(self.imgs[index]) == 2:
                img_path, mask_path = self.imgs[index]
                centroid = None
                class_id = None
            else:
                img_path, mask_path, centroid, class_id = self.imgs[index]

            mask_out = cfg.DATASET.MASK_OUT_CITYSCAPES and \
                cfg.DATASET.CUSTOM_COARSE_PROB is not None and \
                'refinement' in mask_path

            img, mask, img_name = self.read_images(img_path, mask_path,
                                                   mask_out=mask_out)

            ######################################################################
            # Thresholding is done when using coarse-labelled Cityscapes images
            ######################################################################
            if 'refinement' in mask_path:
                mask = np.array(mask)
                prob_mask_path = mask_path.replace('.png', '_prob.png')
                # put it in 0 to 1
                prob_map = np.array(Image.open(prob_mask_path)) / 255.0
                prob_map_threshold = (prob_map < cfg.DATASET.CUSTOM_COARSE_PROB)
                mask[prob_map_threshold] = cfg.DATASET.IGNORE_LABEL
                mask = Image.fromarray(mask.astype(np.uint8))

            # Enforce at least one defect pixel
            mask_np = np.array(mask)
            if (mask_np > 0).sum() > 0:
                break

            # If not valid, retry with a different index
            index = np.random.randint(0, len(self.imgs))
            attempt += 1

        img, mask, scale_float = self.do_transforms(img, mask, centroid,
                                                    img_name, class_id)

        return img, mask, img_name, scale_float

    def fill_trainid_to_name(self):
        self.trainid_to_name[0]  = "background"
        self.trainid_to_name[1]  = "bartack"
        self.trainid_to_name[2]  = "broken_stitches"
        self.trainid_to_name[3]  = "button_hole_"
        self.trainid_to_name[4]  = "damage"
        self.trainid_to_name[5]  = "dirty_soil_stain"
        self.trainid_to_name[6]  = "fabric_defect"
        self.trainid_to_name[7]  = "hole_gash"
        self.trainid_to_name[8]  = "mismatch_pattern"
        self.trainid_to_name[9]  = "missing_component"
        self.trainid_to_name[10]  = "open_seam"
        self.trainid_to_name[11] = "others"
        self.trainid_to_name[12] = "placket"
        self.trainid_to_name[13] = "pleated"
        self.trainid_to_name[14] = "poor_shape"
        self.trainid_to_name[15] = "puckering_fullness"
        self.trainid_to_name[16] = "run_off_stitches"
        self.trainid_to_name[17] = "shading"
        self.trainid_to_name[18] = "skip_stitches"
        self.trainid_to_name[19] = "thread_end_fly_thread"
        self.trainid_to_name[20] = "twisted_roping"
        self.trainid_to_name[21] = "uneven_seam"

    def fill_colormap(self):
        color_map = imgviz.label_colormap()
        palette = []
        for i, color in enumerate(color_map):
            if i < self.num_classes:
                palette.extend(color)
            else:
                break
        zero_pad = 256 * 3 - len(palette)
        palette.extend([0] * zero_pad)
        self.color_mapping = palette

    def fill_colormap_and_names(self, config_fn):
        """
        Mapillary code for color map and class names

        Outputs
        -------
        self.trainid_to_name
        self.color_mapping
        """
        with open(config_fn) as config_file:
            config = json.load(config_file)
        config_labels = config['labels']

        # calculate label color mapping
        colormap = []
        self.trainid_to_name = {}
        for i in range(0, len(config_labels)):
            colormap = colormap + config_labels[i]['color']
            name = config_labels[i]['readable']
            name = name.replace(' ', '_')
            self.trainid_to_name[i] = name
        self.color_mapping = colormap