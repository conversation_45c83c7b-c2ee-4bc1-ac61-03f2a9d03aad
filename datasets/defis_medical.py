"""
Copyright 2020 Nvidia Corporation

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.


WSP Dataset Loader
"""
import os
import json
import glob

from config import cfg
from runx.logx import logx
from datasets.base_loader import BaseLoader
from datasets.utils import make_dataset_folder
from datasets import uniform


class Loader(BaseLoader):
    num_classes = 15  # Updated to match the classmap (18 classes including background)
    ignore_label = 255
    trainid_to_name = {}
    color_mapping = []

    def __init__(self, mode, quality='semantic', joint_transform_list=None,
                 img_transform=None, label_transform=None, eval_folder=None):

        super(Loader, self).__init__(quality=quality,
                                     mode=mode,
                                     joint_transform_list=joint_transform_list,
                                     img_transform=img_transform,
                                     label_transform=label_transform)

        root = cfg.DATASET.DEFIS_MEDICAL_DIR
        #config_fn = os.path.join(root, 'config.json')
        #self.fill_colormap_and_names(config_fn)
        self.fill_colormap()
        self.fill_tainid_to_name()
        print("Initiating DEFIS-Medical Loader")
        ######################################################################
        # Assemble image lists
        ######################################################################
        if mode == 'folder':
            self.all_imgs = make_dataset_folder(eval_folder)
        else:
            splits = {'train': 'train',
                      'val': 'val',
                      'test': 'test'}
            split_name = splits[mode]
            img_ext = 'jpg'
            mask_ext = 'png'
            img_root = os.path.join(root, split_name, 'images')
            mask_root = os.path.join(root, split_name, 'seg')
            self.all_imgs = self.find_images(img_root, mask_root, img_ext,
                                             mask_ext)
        logx.msg('all imgs {}'.format(len(self.all_imgs)))
        self.centroids = uniform.build_centroids(self.all_imgs,
                                                 self.num_classes,
                                                 self.train,
                                                 cv=cfg.DATASET.CV)
        self.build_epoch()


#esta función y la siguiente no están puestas asi(en ese orden) x gusto. tiene q ser asi. RGB
#"""
    # def fill_colormap(self):
        # palette = [0,0,0,#black
                   # 0, 128, 0,#green
                   # 128, 0, 0,#red
                   # 128, 128, 0,#yellow
                   # 0, 0, 128]#blue
        # zero_pad = 256 * 3 - len(palette)
        # for i in range(zero_pad):
            # palette.append(0)
        # self.color_mapping = palette





    # def fill_tainid_to_name(self):
        # #to be changed
        # self.trainid_to_name[0] = "Background"
        # self.trainid_to_name[1] = "reflex_normal"
        # self.trainid_to_name[2] = "damaged"
        # self.trainid_to_name[3] = "reflex_prismen"
        # self.trainid_to_name[4] = "reflex_stripe"
        # #self.trainid_to_name[255] = "Ignore"

    def fill_colormap(self):
        palette = [
            0, 0, 0,         # 0 - Background
            0, 0, 128,       # 1 - hole
            128, 0, 0,       # 2 - strips
            0, 128, 128,     # 3 - stain
            0, 128, 0,       # 4 - line
            128, 0, 128,     # 5 - knots
            128, 128, 0,     # 6 - fiber
            128, 128, 128,   # 7 - surface
            255, 0, 128,     # 8 - elastic
            255, 128, 0,     # 9 - pull
            0, 255, 128,     # 10 - seam
            128, 255, 0,     # 11 - band
            0, 128, 255,     # 12 - pilling
            192, 192, 0,     # 14 - platine
            64, 0, 255       # 15 - colouring_fold
        ]
        zero_pad = 256 * 3 - len(palette)
        for _ in range(zero_pad):
            palette.append(0)
        self.color_mapping = palette
    
    
    
    def fill_tainid_to_name(self):
        #to be changed
        self.trainid_to_name[0] = "Background"
        self.trainid_to_name[1] = "hole"
        self.trainid_to_name[2] = "strips"
        self.trainid_to_name[3] = "stain"
        self.trainid_to_name[4] = "line"
        self.trainid_to_name[5] = "knots"
        self.trainid_to_name[6] = "fiber"
        self.trainid_to_name[7] = "surface"
        self.trainid_to_name[8] = "elastic"
        self.trainid_to_name[9] = "pull"
        self.trainid_to_name[10] = "seam"
        self.trainid_to_name[11] = "band"
        self.trainid_to_name[12] = "pilling"
        self.trainid_to_name[13] = "platine"
        self.trainid_to_name[14] = "colouring_fold"
#        """


    def fill_colormap_and_names(self, config_fn):
        """
        Mapillary code for color map and class names

        Outputs
        -------
        self.trainid_to_name
        self.color_mapping
        """
        with open(config_fn) as config_file:
            config = json.load(config_file)
        config_labels = config['labels']

        # calculate label color mapping
        colormap = []
        self.trainid_to_name = {}
        for i in range(0, len(config_labels)):
            colormap = colormap + config_labels[i]['color']
            name = config_labels[i]['readable']
            name = name.replace(' ', '_')
            self.trainid_to_name[i] = name
        self.color_mapping = colormap
