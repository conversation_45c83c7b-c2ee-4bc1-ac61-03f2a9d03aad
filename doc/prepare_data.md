## 1\. Data preparation

To add new images to the train/test sets on the server, we start by placing them in an intermediate folder, usually called `ORIGINAL`, where we handle splitting and preprocessing. It’s important to check that each image has a matching JSON annotation file from Labelme to keep everything consistent and accurate. Once validated, we convert these annotations to a format that works with HRNet, creating grayscale images where values from 1 to 255 represent different classes, and 0 is reserved for the background. This approach keeps our data organized and ready for effective training and testing.

Steps:

1.  Split the files into train and test to be able to use the same splits with different models/arch and compare them
    
2.  Check the that pairs json and jpg are correct . Example  
    `python utils/check_pairs.py files /mnt/data/datasets/nips/ORIGINAL/added_2024-05-15/test/labelme /mnt/data/datasets/nips/ORIGINAL/added_2024-05-15/test/todel &nbsp;--extensions jpg json`
    
3.  Make sure all the images has the same orientation&nbsp;  
    `python utils/rotate_dataset.py vertical /mnt/data/datasets/nips/ORIGINAL/added_2024-10-27/test /mnt/data/datasets/nips/ORIGINAL/added_2024-10-27/test-rotated`
    
    1.  You can check if there is any image in horizonal like this:  
        `python utils/rotate_dataset.py horizontal /mnt/data/datasets/nips/ORIGINAL/added_2024-10-27/test --check`
        
    2.  Make sure that the labels were correctly translated by opening the output folder with labelme  
        `/opt/labelme-Linux /mnt/data/datasets/nips/ORIGINAL/added_2024-10-27/test-rotated/`
        
4.  Run Prepare data:
    
    `PYTHONPATH=".:libs/hrnet" python utils/prepare_data.py /mnt/data/datasets/nips/ORIGINAL/added_2024-10-09/train --classmap dev/class_map.yaml --workers 16`
    
    1.   You can perform a validation at any time by running something like this. Verify visually that the color correspond witht the seg\_color folder  
        `PYTHONPATH=".:libs/hrnet" python utils/prepare_data.py /mnt/data/datasets/nips/ORIGINAL/added_2024-10-09/train/ --classmap configs/nips/class_map.yaml --workers 32 --start_step 3  --validate`
        

<br>
