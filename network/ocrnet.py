"""
Copyright 2020 Nvidia Corporation

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
"""
from torch import nn
import torch
import torch.nn.functional as F

from network.mynn import initialize_weights, Upsample, scale_as
from network.mynn import ResizeX
from network.utils import get_trunk
from network.utils import BNReLU, get_aspp
from network.utils import make_attn_head
from network.ocr_utils import SpatialGather_Module, SpatialOCR_Module
from config import cfg
from utils.misc import fmt_scale


class OCR_block(nn.Module):
    """
    Some of the code in this class is borrowed from:
    https://github.com/HRNet/HRNet-Semantic-Segmentation/tree/HRNet-OCR
    """
    def __init__(self, high_level_ch):
        super(OCR_block, self).__init__()

        ocr_mid_channels = cfg.MODEL.OCR.MID_CHANNELS
        ocr_key_channels = cfg.MODEL.OCR.KEY_CHANNELS
        num_classes = cfg.DATASET.NUM_CLASSES

        self.conv3x3_ocr = nn.Sequential(
            nn.Conv2d(high_level_ch, ocr_mid_channels,
                      kernel_size=3, stride=1, padding=1),
            BNReLU(ocr_mid_channels),
        )
        self.ocr_gather_head = SpatialGather_Module(num_classes)
        self.ocr_distri_head = SpatialOCR_Module(in_channels=ocr_mid_channels,
                                                 key_channels=ocr_key_channels,
                                                 out_channels=ocr_mid_channels,
                                                 scale=1,
                                                 dropout=0.05,
                                                 )
        self.cls_head = nn.Conv2d(
            ocr_mid_channels, num_classes, kernel_size=1, stride=1, padding=0,
            bias=True)

        self.aux_head = nn.Sequential(
            nn.Conv2d(high_level_ch, high_level_ch,
                      kernel_size=1, stride=1, padding=0),
            BNReLU(high_level_ch),
            nn.Conv2d(high_level_ch, num_classes,
                      kernel_size=1, stride=1, padding=0, bias=True)
        )

        if cfg.OPTIONS.INIT_DECODER:
            initialize_weights(self.conv3x3_ocr,
                               self.ocr_gather_head,
                               self.ocr_distri_head,
                               self.cls_head,
                               self.aux_head)

    def forward(self, high_level_features):
        feats = self.conv3x3_ocr(high_level_features)
        aux_out = self.aux_head(high_level_features)
        context = self.ocr_gather_head(feats, aux_out)
        ocr_feats = self.ocr_distri_head(feats, context)
        cls_out = self.cls_head(ocr_feats)
        return cls_out, aux_out, ocr_feats


class OCRNet(nn.Module):
    """
    OCR net
    """
    def __init__(self, num_classes, trunk='hrnetv2', criterion=None):
        super(OCRNet, self).__init__()
        self.criterion = criterion
        self.backbone, _, _, high_level_ch = get_trunk(trunk)
        self.ocr = OCR_block(high_level_ch)

    def forward(self, inputs):
        assert 'images' in inputs
        x = inputs['images']

        _, _, high_level_features = self.backbone(x)
        cls_out, aux_out, _ = self.ocr(high_level_features)
        aux_out = scale_as(aux_out, x)
        cls_out = scale_as(cls_out, x)

        if self.training:
            gts = inputs['gts']
            # cache for FG/BG auxiliary
            self.last_logits = cls_out
            # standard OCR losses
            aux_loss = self.criterion(aux_out, gts, do_rmi=cfg.LOSS.OCR_AUX_RMI)
            main_loss = self.criterion(cls_out, gts)
            loss = cfg.LOSS.OCR_ALPHA * aux_loss + main_loss
            # optional FG/BG loss
            if getattr(cfg.LOSS, 'FG_AUX_WEIGHT', 0) > 0:
                # compute binary logits: logit_fg = logsumexp over classes>0 minus background logit
                fg_logits = torch.logsumexp(cls_out[:, 1:, :, :], dim=1) - cls_out[:, 0, :, :]
                fg_targets = (gts > 0).float()
                bce = F.binary_cross_entropy_with_logits(fg_logits, fg_targets, reduction='mean')
                loss = loss + cfg.LOSS.FG_AUX_WEIGHT * bce
            return loss
        else:
            return {'pred': cls_out}


class OCRNetASPP(nn.Module):
    """
    OCR net with ASPP
    """
    def __init__(self, num_classes, trunk='hrnetv2', criterion=None):
        super(OCRNetASPP, self).__init__()
        self.criterion = criterion
        self.backbone, _, _, high_level_ch = get_trunk(trunk)
        self.aspp, aspp_out_ch = get_aspp(high_level_ch,
                                          bottleneck_ch=256,
                                          output_stride=8)
        self.ocr = OCR_block(aspp_out_ch)

    def forward(self, inputs):
        assert 'images' in inputs
        x = inputs['images']

        _, _, high_level_features = self.backbone(x)
        aspp_feats = self.aspp(high_level_features)
        cls_out, aux_out, _ = self.ocr(aspp_feats)
        aux_out = scale_as(aux_out, x)
        cls_out = scale_as(cls_out, x)

        if self.training:
            gts = inputs['gts']
            # cache for FG/BG auxiliary
            self.last_logits = cls_out
            loss = cfg.LOSS.OCR_ALPHA * self.criterion(aux_out, gts) + self.criterion(cls_out, gts)
            if getattr(cfg.LOSS, 'FG_AUX_WEIGHT', 0) > 0:
                fg_logits = torch.logsumexp(cls_out[:, 1:, :, :], dim=1) - cls_out[:, 0, :, :]
                fg_targets = (gts > 0).float()
                bce = F.binary_cross_entropy_with_logits(fg_logits, fg_targets, reduction='mean')
                loss = loss + cfg.LOSS.FG_AUX_WEIGHT * bce
            return loss
        else:
            return {'pred': cls_out}


class MscaleOCR(nn.Module):
    """
    Multi-scale OCR net
    """
    def __init__(self, num_classes, trunk='hrnetv2', criterion=None):
        super(MscaleOCR, self).__init__()
        self.criterion = criterion
        self.backbone, _, _, high_level_ch = get_trunk(trunk)
        self.ocr = OCR_block(high_level_ch)
        self.scale_attn = make_attn_head(
            in_ch=cfg.MODEL.OCR.MID_CHANNELS, out_ch=1)

    def _fwd(self, x):
        x_size = x.size()[2:]

        _, _, high_level_features = self.backbone(x)
        cls_out, aux_out, ocr_mid_feats = self.ocr(high_level_features)
        attn = self.scale_attn(ocr_mid_feats)

        aux_out = Upsample(aux_out, x_size)
        cls_out = Upsample(cls_out, x_size)
        attn = Upsample(attn, x_size)

        return {'cls_out': cls_out,
                'aux_out': aux_out,
                'logit_attn': attn}

    def nscale_forward(self, inputs, scales):
        x_1x = inputs['images']
        assert 1.0 in scales, 'expected 1.0 in scales'
        scales = sorted(scales, reverse=True)

        pred = None
        aux = None
        for s in scales:
            x = ResizeX(x_1x, s)
            outs = self._fwd(x)
            cls_out, attn_out, aux_out = outs['cls_out'], outs['logit_attn'], outs['aux_out']

            if pred is None:
                pred, aux = cls_out, aux_out
            elif s >= 1.0:
                # upsample previous before multiply
                pred = scale_as(pred, cls_out)
                aux = scale_as(aux, cls_out)
                attn_out = attn_out
                pred = attn_out * cls_out + (1 - attn_out) * pred
                aux  = attn_out * aux_out + (1 - attn_out) * aux
            else:
                # s<1.0: upsample both
                w = scale_as(attn_out * cls_out, pred)
                pred = w + (1 - scale_as(attn_out, pred)) * pred
                aux  = scale_as(attn_out * aux_out, pred) + (1 - scale_as(attn_out, pred)) * aux

        if self.training:
            gts = inputs['gts']
            self.last_logits = pred
            loss = cfg.LOSS.OCR_ALPHA * self.criterion(aux, gts) + self.criterion(pred, gts)
            if getattr(cfg.LOSS, 'FG_AUX_WEIGHT', 0) > 0:
                fg_logits = torch.logsumexp(pred[:,1:,:,:], dim=1) - pred[:,0,:,:]
                fg_targets = (gts>0).float()
                bce = F.binary_cross_entropy_with_logits(fg_logits, fg_targets, reduction='mean')
                loss = loss + cfg.LOSS.FG_AUX_WEIGHT * bce
            return loss
        else:
            return {'pred': pred}

    def two_scale_forward(self, inputs):
        x_1x = inputs['images']
        # low-res
        lo = ResizeX(x_1x, cfg.MODEL.MSCALE_LO_SCALE)
        lo_outs = self._fwd(lo)
        p_lo, a_lo = lo_outs['cls_out'], lo_outs['logit_attn']
        # high-res
        hi_outs = self._fwd(x_1x)
        p_hi, a_hi = hi_outs['cls_out'], hi_outs['logit_attn']

        # first align resolutions
        p_lo       = scale_as(p_lo, p_hi)
        aux_lo     = scale_as(lo_outs['aux_out'], p_hi)
        a_hi       = scale_as(a_hi, p_hi)
        # then fuse
        p_lo       = a_hi * p_lo
        joint      = p_lo + (1 - a_hi) * p_hi
        aux_lo     = aux_lo

        if self.training:
            gts = inputs['gts']
            self.last_logits = joint
            do_rmi = cfg.LOSS.OCR_AUX_RMI
            aux_loss = self.criterion(aux_lo, gts, do_rmi=do_rmi)
            main_loss = self.criterion(joint, gts, do_rmi=True)
            loss = cfg.LOSS.OCR_ALPHA * aux_loss + main_loss
            if cfg.LOSS.SUPERVISED_MSCALE_WT:
                loss_lo = self.criterion(scale_as(lo_outs['cls_out'], joint), gts, do_rmi=False)
                loss_hi = self.criterion(p_hi, gts, do_rmi=False)
                loss += cfg.LOSS.SUPERVISED_MSCALE_WT * (loss_lo + loss_hi)
            # FG/BG aux
            if getattr(cfg.LOSS, 'FG_AUX_WEIGHT', 0) > 0:
                fg_logits = torch.logsumexp(joint[:,1:,:,:], dim=1) - joint[:,0,:,:]
                fg_targets = (gts>0).float()
                bce = F.binary_cross_entropy_with_logits(fg_logits, fg_targets, reduction='mean')
                loss = loss + cfg.LOSS.FG_AUX_WEIGHT * bce
            return loss
        else:
            return {'pred': joint}

    def forward(self, inputs):
        if cfg.MODEL.N_SCALES and not self.training:
            return self.nscale_forward(inputs, cfg.MODEL.N_SCALES)
        return self.two_scale_forward(inputs)


def HRNet(num_classes, criterion):
    return OCRNet(num_classes, trunk='hrnetv2', criterion=criterion)


def HRNet_Mscale(num_classes, criterion):
    return MscaleOCR(num_classes, trunk='hrnetv2', criterion=criterion)
