# File: prepare_data.py
# Author: <PERSON><PERSON>
# Copyright: Desion GmbH
# Description:
#     The process for being able to train HRNET with new data contained quite a few steps that were bothersome to execute each time. This script summarizes these.
#     It includes:
#     - fixing the order of the labels in the json
#     - converting the json label to PNG segmentation label
#     - converting these to the class values (Greyscale)
#
# Changelog:
#     2024-10-10: Optimized "process_json_file" to use concurrency
#     2025-05-02: Added --classmap and --labelme_dir CLI flags and removed hard‑coded paths
#

import argparse
import concurrent.futures
import glob
import json
import os
import shutil
import multiprocessing

import cv2
import imgviz
import labelme
import numpy as np
from tqdm import tqdm

from utils.data_converter import yaml_file_to_dict, json_file_to_dict, dict_to_json_file

# Add class map and label aliases for normalization and robust mapping
CLASS_MAP = {
    0: "hole",
    1: "strips",
    2: "stain",
    3: "line",
    4: "knots",
    5: "fiber",
    6: "surface",
    7: "elastic",
    8: "pull",
    9: "seam",
    10: "band",
    11: "pilling",
    12: "platine",
    13: "colouring fold"
}

LABEL_ALIASES = {
    "strip": "strips",
    "strips": "strips",
    "stain": "stain",
    "pull": "pull",
    "knot": "knots",
    "knots": "knots",
    "seam": "seam",
}

def normalize_label(label):
    """Normalize label using LABEL_ALIASES and lowercasing."""
    label = label.lower().strip()
    return LABEL_ALIASES.get(label, label)

def fix_label_order(path, classmap, verbose=True, num_workers: int = 8):
    """Fix the order of labels in JSON annotation files by following the ordering
    specified in *classmap*.
    """

    ordered_labels = [normalize_label(l) for l in classmap.keys()]

    def reorder_file(ann_file: str, path: str, ordered_labels: list[str]):
        input_file = os.path.join(path, ann_file)
        try:
            anno = json_file_to_dict(input_file)

            def get_label_index(label: str):
                norm_label = normalize_label(label)
                if norm_label not in ordered_labels:
                    raise ValueError(
                        f"Label '{label}' (normalized: '{norm_label}') not found in the ordered labels from the classmap. File: {input_file}"
                    )
                return ordered_labels.index(norm_label)

            anno["shapes"] = sorted(anno["shapes"], key=lambda x: get_label_index(x["label"]))
            dict_to_json_file(anno, input_file)
        except Exception as e:  # noqa: BLE001, E722
            print(f"Error processing {input_file}: {e}")

    json_files = [f for f in os.listdir(path) if f.endswith(".json")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(reorder_file, file, path, ordered_labels) for file in json_files]
        for result in tqdm(
            concurrent.futures.as_completed(futures), total=len(json_files), desc="Processing files", disable=not verbose
        ):
            result.result()

    if verbose:
        print("Label order fixing completed. Original files have been updated.")


def process_json_file(
    filename: str,
    image_dir: str,
    seg_dir: str,
    class_name_to_id: dict[str, int],
    labelme_dir: str,
    no_compress: bool = False,
):
    base = os.path.splitext(os.path.basename(filename))[0]
    out_img_file = os.path.join(image_dir, f"{base}.jpg")
    out_png_file = os.path.join(seg_dir, f"{base}.png")

    try:
        process_label_file(filename, out_img_file, out_png_file, class_name_to_id, no_compress)
    except Exception as e:  # noqa: BLE001, E722
        fix_annotation_file(filename)
        try:
            process_label_file(filename, out_img_file, out_png_file, class_name_to_id, no_compress)
        except Exception as e:  # noqa: BLE001, E722
            print(f"Problem with file {filename}: {e}")
            tofix_dir = os.path.join(os.path.dirname(labelme_dir), "labelme_tofix")
            os.makedirs(tofix_dir, exist_ok=True)
            for ext in [".json", ".jpg", ".png"]:
                src = os.path.join(labelme_dir, f"{base}{ext}")
                if os.path.exists(src):
                    shutil.move(src, os.path.join(tofix_dir, f"{base}{ext}"))


def process_label_file(
    filename: str,
    out_img_file: str,
    out_png_file: str,
    class_name_to_id: dict[str, int],
    no_compress: bool = False,
):
    label_file = labelme.LabelFile(filename)

    with open(out_img_file, "wb") as f:
        if no_compress:
            f.write(label_file.imageData)
        else:
            img_array = labelme.utils.img_data_to_arr(label_file.imageData)
            success = cv2.imwrite(out_img_file, cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR))
            if not success:
                raise RuntimeError(f"Failed to save compressed image: {out_img_file}")

    img = labelme.utils.img_data_to_arr(label_file.imageData)
    # Normalize all shape labels before mapping to class ids
    shapes = []
    for s in label_file.shapes:
        s = dict(s)
        s["label"] = normalize_label(s["label"])
        shapes.append(s)
    lbl, _ = labelme.utils.shapes_to_label(img.shape, shapes, class_name_to_id)
    labelme.utils.lblsave(out_png_file, lbl)


def fix_annotation_file(filename: str):
    data = json_file_to_dict(filename)
    data["shapes"] = [s for s in data["shapes"] if len(s["points"]) > 2]
    dict_to_json_file(data, filename)


def json_to_seg_image(
    labelme_dir: str,
    seg_dir: str,
    classmap: dict,
    num_workers: int = 8,
    no_compress: bool = False,
):
    os.makedirs(seg_dir, exist_ok=True)

    class_names = list(classmap.keys())
    assert class_names[0].replace("_", "") == "background", "Class 'background' must be the class [0]"

    class_name_to_id = {name: i for i, name in enumerate(class_names)}
    print("class_names:", class_names)

    json_files = glob.glob(os.path.join(labelme_dir, "*.json"))
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [
            executor.submit(
                process_json_file,
                filename,
                labelme_dir,
                seg_dir,
                class_name_to_id,
                labelme_dir,
                no_compress,
            )
            for filename in json_files
        ]
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(json_files), desc="Processing JSON files"):
            pass


def colours_to_gray_seg(
    input_path: str,
    output_path: str,
    color2index: dict[tuple[int, int, int], int],
    num_workers: int = 8,
):
    os.makedirs(output_path, exist_ok=True)

    lut = np.zeros((256, 256, 256), dtype=np.uint8)
    for color, index in color2index.items():
        lut[color[0], color[1], color[2]] = index

    def process_image(file: str):
        if not file.endswith(".png"):
            return
        try:
            im = cv2.imread(os.path.join(input_path, file))
            if im is None:
                raise ValueError(f"Failed to read image: {file}")
            im_rgb = cv2.cvtColor(im, cv2.COLOR_BGR2RGB)
            new_im = lut[im_rgb[:, :, 0], im_rgb[:, :, 1], im_rgb[:, :, 2]]
            cv2.imwrite(os.path.join(output_path, file), new_im)
        except Exception as e:  # noqa: BLE001, E722
            print(f"Failed processing {file}: {e}")

    files = [f for f in os.listdir(input_path) if f.endswith(".png")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(process_image, file) for file in files]
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(files), desc="Processing images"):
            pass


def validate_gray_seg(
    seg_dir: str,
    output_dir: str,
    color_map: np.ndarray,
    num_workers: int = 8,
):
    os.makedirs(output_dir, exist_ok=True)

    def process_image(file: str):
        if not file.endswith(".png"):
            return
        try:
            gray_img = cv2.imread(os.path.join(seg_dir, file), cv2.IMREAD_GRAYSCALE)
            if gray_img is None:
                raise ValueError(f"Failed to read image: {file}")
            colored_img = np.zeros((gray_img.shape[0], gray_img.shape[1], 3), dtype=np.uint8)
            for idx, color in enumerate(color_map):
                colored_img[gray_img == idx] = color
            cv2.imwrite(os.path.join(output_dir, file), cv2.cvtColor(colored_img, cv2.COLOR_RGB2BGR))
        except Exception as e:  # noqa: BLE001, E722
            print(f"Failed processing {file}: {e}")

    files = [f for f in os.listdir(seg_dir) if f.endswith(".png")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(process_image, file) for file in files]
        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(files), desc="Validating images"):
            pass


if __name__ == "__main__":
    parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument(
        "root_dir",
        help="Root folder containing the dataset. All output (seg, seg_colour, seg_validation) will be placed inside this directory.",
    )
    parser.add_argument(
        "--classmap",
        required=True,
        help="Path to the YAML class‑map file (absolute or relative). Replaces the previously hard‑coded path.",
    )
    parser.add_argument(
        "--labelme_dir",
        default="images",
        help="Directory *inside* root_dir that contains the LabelMe images and JSON files.",
    )
    parser.add_argument(
        "--start_step",
        default=0,
        type=int,
        help="0: Fix label order, 1: JSON to seg, 2: colours to gray, 3: Validate grayscale images",
    )
    parser.add_argument("--not-verbose", action="store_true", help="Disable verbose output")
    parser.add_argument("--validate", action="store_true", help="Validate gray images after processing")
    parser.add_argument("--no-compress", action="store_true", help="Disable image compression when saving")
    args = parser.parse_args()

    labelme_dir = os.path.join(args.root_dir, args.labelme_dir)
    image_dir = labelme_dir  # keep the legacy variable name for downstream code

    output_seg = os.path.join(args.root_dir, "seg")
    output_seg_colour = os.path.join(args.root_dir, "seg_colour")
    output_seg_validation = os.path.join(args.root_dir, "seg_validation")

    classmap = yaml_file_to_dict(os.path.abspath(args.classmap))
    color_map = imgviz.label_colormap()
    available_threads = multiprocessing.cpu_count()

    verbose = not args.not_verbose

    if args.start_step == 0:
        print("Step 0. Fixing label order → Garments → Stripes → Defects")
        fix_label_order(labelme_dir, classmap, verbose)
        args.start_step += 1

    if args.start_step == 1:
        os.makedirs(output_seg_colour, exist_ok=True)
        print("Step 1. Generating colour segmentation images")
        json_to_seg_image(
            labelme_dir,
            output_seg_colour,
            classmap,
            num_workers=available_threads,
            no_compress=args.no_compress,
        )
        args.start_step += 1

    if args.start_step == 2:
        color2index = {(c[0], c[1], c[2]): i for i, c in enumerate(color_map)}
        print("Step 2. Converting colour segmentations to grayscale IDs")
        colours_to_gray_seg(output_seg_colour, output_seg, color2index, num_workers=available_threads)
        if args.validate:
            args.start_step += 1

    if args.start_step == 3:
        print("Step 3. Validating grayscale images against colour originals")
        validate_gray_seg(output_seg, output_seg_validation, color_map, num_workers=available_threads)
        print("Validation complete. Compare 'seg_colour' with 'seg_validation'. They should be identical.")
