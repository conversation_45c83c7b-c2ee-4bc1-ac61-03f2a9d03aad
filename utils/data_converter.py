"""
# File: data_converter.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
    Collection of utilities to manage data from documents such as xml, json and yaml.
    It also provides function to convert from/to python dictionaries
# Changelog:
#   - 14.10.2023. <PERSON>. Implemented json_string_to_dict
"""
import json
import os
import queue
from pathlib import Path

import xmltodict
# import xmltodict
import yaml


def json_to_dict(json_path) -> dict:
    """
    Reads json file and convert in a python dictionary
    :param json_path: Path to json file to be converted
    :return: The dict object is the parsed json
    """
    with open(json_path, "r") as f:
        return json.load(f)


def dict_to_json(data, output_path=None) -> str:
    """
    Parse a dictionary into a json and save on disk if requested. If parent folder does not exist,
    then it creates it before save the file.
    :param data: Dictionary to be parsed to json
    :param output_path: Path to save the json file generated. Path *MUST* include filename and extension
    :return: The xml in string format is the parse
    """

    # d = json.dumps(str(data), indent=4)
    d = json.dumps(data, indent=4)

    if output_path is not None:

        # Ensure parent folder exist
        parent = Path(output_path).parent
        if parent:
            os.makedirs(parent, exist_ok=True)

        with open(output_path, "w") as f:
            f.write(d)
    return d


def xml_string_to_dict(xmlstr: str) -> dict:
    """Convert an XML string to a Python dictionary."""
    try:
        return xmltodict.parse(xmlstr)
    except Exception as e:
        raise ValueError("Failed to parse XML string. Error: {}".format(e))


def xml_file_to_dict(xml_path: str) -> dict:
    """Reads XML file and convert it to a Python dictionary."""
    if not Path(xml_path).is_file():
        raise FileNotFoundError(f"File '{xml_path}' does not exist.")
    try:
        with open(xml_path, "r") as f:
            return xml_string_to_dict(f.read())
    except Exception as e:
        raise ValueError("Failed to parse XML file. Error: {}".format(e))


def dict_to_xml_string(data: dict) -> str:
    """Parse a dictionary into an XML string."""
    try:
        return xmltodict.unparse(data, pretty=True)
    except Exception as e:
        raise ValueError("Failed to convert dictionary to XML string. Error: {}".format(e))


def dict_to_xml_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into an XML and save it to the disk."""
    xml_string = dict_to_xml_string(data)

    # Ensure parent folder exist
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(xml_string)
    except Exception as e:
        raise ValueError("Failed to write XML to file. Error: {}".format(e))


def json_string_to_dict(json_str: str) -> dict:
    """Convert a JSON string to a Python dictionary."""
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError("Failed to decode JSON string. Error: {}".format(e))


def json_file_to_dict(json_path: str) -> dict:
    """Reads JSON file and convert it to a Python dictionary."""
    if not Path(json_path).is_file():
        raise FileNotFoundError(f"File '{json_path}' does not exist.")
    try:
        with open(json_path, "r") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError("Failed to decode JSON file. Error: {}".format(e))


def dict_to_json_string(data: dict) -> str:
    """Parse a dictionary into a JSON string."""
    try:
        return json.dumps(data, indent=4)
    except Exception as e:
        raise ValueError("Failed to convert dictionary to JSON string. Error: {}".format(e))


def dict_to_json_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into a JSON and save it to the disk."""
    json_string = dict_to_json_string(data)

    # Ensure parent folder exist
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(json_string)
    except Exception as e:
        raise ValueError("Failed to write JSON to file. Error: {}".format(e))


def yaml_file_to_dict(path: str) -> dict:
    """Load a YAML configuration file."""
    if not Path(path).is_file():
        raise FileNotFoundError(f"The configuration file '{path}' does not exist.")

    with open(path, 'r') as file:
        try:
            return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ValueError(f"The configuration file '{path}' is not a valid YAML file. Error: {str(e)}")


def dict_to_yaml_file(data: dict, output_path: str) -> None:
    """Parse a dictionary into YAML and save it to the disk."""
    yaml_string = yaml.dump(data, default_flow_style=False)

    # Ensure parent folder exists
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(output_path, "w") as f:
            f.write(yaml_string)
    except Exception as e:
        raise ValueError("Failed to write YAML to file. Error: {}".format(e))


def copy_queue(src_queue) -> queue:
    """
    Copy the contents of a source queue to a new queue, while keeping the original queue intact.

    :param src_queue: (queue.Queue) The source queue to copy from.
    :return: (queue.Queue) The new queue containing the copied items.
    """
    copy_q = queue.Queue()
    q_size = src_queue.qsize()
    for i in range(q_size):
        item = src_queue.get()
        copy_q.put(item)
        src_queue.put(item)  # Put the item back into the original queue

    return copy_q
