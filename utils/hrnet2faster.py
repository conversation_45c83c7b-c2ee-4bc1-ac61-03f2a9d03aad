import enum
import cv2
import os
import numpy as np
import argparse
import json

parser = argparse.ArgumentParser(
    formatter_class=argparse.ArgumentDefaultsHelpFormatter
)
parser.add_argument("--input_dir", help="input annotated directory")
parser.add_argument("--output_dir", help="output annotation directory")
parser.add_argument("--color_map", help="json with color to class mappings")
parser.add_argument("--vis_only", action="store_true", help="only do visualisation and don't save labels")
parser.add_argument("--bbox_threshold", help="min bounding box area",default=100, type=int)
args = parser.parse_args()

os.makedirs(args.output_dir, exist_ok=True)

wsp_mode = False
if wsp_mode:
    distance_threshold = 1.0
    textile_types = ["hose","jacke","latzhose","t-shirt"]
else:
    textile_types = ["mewatex","mewatex_plus","mewatex_rot","mewatex_blau","mewatex_gruen","protex_blau","protex_rot","protex_grau","ultra_blau","ultra_rot","ultra_grau","mewatex_plus_rot","mewatex_plus_gruen","mewatex_plus_blau"]

with open(args.color_map,"r") as jsonfile:
    color_map = json.load(jsonfile)

def get_bb_size(bb):
    return abs((bb[2]-bb[0]))*abs((bb[3]-bb[1]))

def compare_bbs(bb1,bb2):
    bb1_size = get_bb_size(bb1)
    bb2_size = get_bb_size(bb2)
    if bb2_size > bb1_size:
        return 1
    elif bb1_size == bb2_size:
        return 0
    else:
        return -1

def rnd(floatnumber):
    return int(round(float(floatnumber)))

for annofile in os.listdir(args.input_dir):
    print(annofile)
    annoimg = cv2.imread(os.path.join(args.input_dir,annofile))
    annoimg = cv2.cvtColor(annoimg, cv2.COLOR_BGR2RGB)
    #annoimg = annoimg[:-200,:]
    #annoimg_hsv = cv2.cvtColor(annoimg, cv2.COLOR_BGR2HSV)
    #h,_,_ = cv2.split(annoimg_hsv)
    #bins = np.bincount(h.flatten())
    #colour_pct = (1.0/20.0)
    #peaks = np.array(np.where(bins > (h.size * colour_pct))[0])
    #print(peaks)
    bb_biggest_textile = [0,0,0,0]
    label_biggest_textile = "background"
    rois = []
    labels = []
    if args.vis_only:
        cv2.imshow("input",annoimg)
    for color_name in color_map.keys():
        if color_name == "background":
            continue
        color_rgb = (color_map[color_name][0], color_map[color_name][1], color_map[color_name][2])
        mask = cv2.inRange(annoimg,color_rgb,color_rgb)
        blob = cv2.bitwise_and(annoimg,annoimg, mask=mask)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if color_name in textile_types:
            contour_list = []
            avg_contour_y = 0
            if wsp_mode:
                # In WSP there are often false classifications at the bottom of the image. This tries to avoid that.
                for contour in contours:
                    #if color_name=="hose":
                        #print(contour)
                    max_contour_y = 0
                    for point in contour:
                        if point[0][1] > max_contour_y:
                            max_contour_y = point[0][1]
                    avg_contour_y = (avg_contour_y+max_contour_y)/2
                    #cv2.drawContours(annoimg, [contour], 0, 255, -1)
                for contour in contours:
                    max_contour_y = 0
                    for point in contour:
                        if point[0][1] > max_contour_y:
                            max_contour_y = point[0][1]
                    if abs(avg_contour_y-max_contour_y) < annoimg.shape[0] * distance_threshold:
                        contour_list.extend(contour)
            else:
                for contour in contours:
                    #print(contour)
                    contour_list.extend(contour)

            contour_list = np.array(contour_list).reshape((-1,1,2)).astype(np.int32)
            hull = cv2.convexHull(contour_list)
            bbox = cv2.boundingRect(hull)
            #print(bbox)
            if hull is None:
                continue
            #cv2.drawContours(annoimg, [hull],0,(255,255,255))
            if compare_bbs(bb_biggest_textile,bbox) > 0:
                bb_biggest_textile = bbox
                label_biggest_textile = color_name
            
        else:
            for contour in contours:
                bbox = cv2.boundingRect(contour)
                top_left, bottom_right = (bbox[0], bbox[1]), (bbox[0]+bbox[2], bbox[1]+bbox[3])
                bbox_area = bbox[2] * bbox[3]
                if bbox_area > args.bbox_threshold:
                    rois.append(bbox)
                    labels.append(color_name)
                    if args.vis_only:
                        cv2.rectangle(annoimg, top_left, bottom_right, color_rgb, 2)
        
        #bbox = cv2.boundingRect(hull)
        
        #bbox = cv2.boundingRect(contour)
        #contour_mask = np.zeros_like(mask)
        #cv2.drawContours(contour_mask, contours, j, 255, -1)
        #result = cv2.bitwise_and(blob, blob, mask=contour_mask)
        #top_left, bottom_right = (bbox[0], bbox[1]), (bbox[0]+bbox[2], bbox[1]+bbox[3])
        #cv2.rectangle(annoimg, top_left, bottom_right, color_rgb, 2)
    if args.vis_only:
        top_left, bottom_right = (bb_biggest_textile[0], bb_biggest_textile[1]), (bb_biggest_textile[0]+bb_biggest_textile[2], bb_biggest_textile[1]+bb_biggest_textile[3])
        cv2.rectangle(annoimg, top_left, bottom_right, color_map[label_biggest_textile], 2)
        cv2.imshow("result",annoimg)
        cv2.waitKey(0)        
    else:
        with open(os.path.join(args.output_dir, annofile.replace("_prediction","")[:-4]+".xml"),"w+") as writefile:
            writefile.write(
                """<annotation>
        <folder>Images</folder>
        <filename>{}</filename>
        <path>{}</path>
        <source>
            <database>Unknown</database>
        </source>
        <size>
            <width>1440</width>
            <height>1080</height>
            <depth>3</depth>
        </size>
        <segmented>0</segmented>\n""".format(annofile[:-4]+".PNG",os.path.join(args.output_dir, annofile[:-4]+".xml")))
            x,y,w,h = bb_biggest_textile
            x2 = x+w
            y2 = y+h
            writefile.write(
                        """        <object>
            <name>{}</name>
            <pose>Unspecified</pose>
            <truncated>0</truncated>
            <difficult>0</difficult>
            <bndbox>
                <xmin>{}</xmin>
                <ymin>{}</ymin>
                <xmax>{}</xmax>
                <ymax>{}</ymax>
            </bndbox>
        </object>\n""".format(label_biggest_textile, int(rnd(x)), int(rnd(y)), int(rnd(x2)), int(rnd(y2))))
            
            for i in range(len(rois)):
                x,y,w,h = rois[i]
                x2 = x+w
                y2 = y+h
                label = labels[i]
                writefile.write(
                        """        <object>
            <name>{}</name>
            <pose>Unspecified</pose>
            <truncated>0</truncated>
            <difficult>0</difficult>
            <bndbox>
                <xmin>{}</xmin>
                <ymin>{}</ymin>
                <xmax>{}</xmax>
                <ymax>{}</ymax>
            </bndbox>
        </object>\n""".format(label, int(rnd(x)), int(rnd(y)), int(rnd(x2)), int(rnd(y2))))
            writefile.write(
                    """</annotation>\n"""
                )