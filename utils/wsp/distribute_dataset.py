"""
# File: distribute_dataset.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
    This script moves (arranges) the images and corresponding JSON files according to the reflective band type and the damages.
    Specifically, it works in a folder with all images and annos and set them as follow:
    -All-images.
        -normal
            -damage
            -no-damage
            
        -stripes
            -damage
            -no-damage
            
        -prismen
        
        -background
        
    You can set how many "damaged" labels a JSON files can have in order to move the JSON and image to the folder damage or no-damage
    Remember that, for now, we have not labeled damages in prismen clothes.
    Pay attention whether images are jpg or png !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
"""


import json
import os
import shutil


def reflex_type(A):
    """
    This function receives the atributte "shapes" from the JSON. "shapes" is a list composed of dictionaries. Each dictionary has the elements "label", "points", ...
    It finds the type of reflex_stripe and returns a string with that information.
    
    :param A: list composed of dictionaries.
    :return aux: string with the type of reflective band (reflex_normal, reflex_stripe or reflex_prismen)  
    """
    i = 0
    aux = 'nothing'
    continu = True

    while ((i < len(A)) and (continu == True)):
        label = A[i]['label']
        if (label == 'reflex_normal'):
            aux = 'reflex_normal'
            continu = False

        elif (label == 'reflex_prismen'):
            aux = 'reflex_prismen'
            continu = False

        elif (label == 'reflex_stripe'):
            aux = 'reflex_stripe'
            continu = False

        else:
            i = i + 1

    return aux

def damages_json(A):
    """
    This function receives the atributte "shapes" from the JSON. "shapes" is a list composed of dictionaries. Each dictionary has the elements "label", "points", ...
    It counts how many "damaged" labels there are and returns True or False according to that number and the one that we set.
    
    :param A: list composed of dictionaries.
    :return aux: string with the type of reflective band (reflex_normal, reflex_stripe or reflex_prismen)  
    """
    n_damages = 0
    for i in range(0, len(A)):
        if (A[i]['label'] == 'damaged'):
            n_damages = n_damages + 1

    if (n_damages > 2):
        return True
    else:
        return  False


if __name__ == "__main__":
    path = 'todas_juntas/'
    #output_path = 'json-normal-segundas/'

    for ann_name in os.listdir(path):
        if ann_name[-4:] == "json" :
            file_path = path + ann_name
            with open(file_path,'r') as jsonfile:
                anno = json.load(jsonfile)#this is a dictionary

            anno_shapes = anno['shapes']#this is a list composed of dictinaries.

            r_type = reflex_type(anno_shapes)
            damages = damages_json(anno_shapes)

            if r_type == 'reflex_normal':
                if damages == True :
                    shutil.move(file_path, path + 'normal/damage/')
                    shutil.move(file_path.replace('json', 'png'), path + 'normal/damage/')
                else:
                    shutil.move(file_path, path + 'normal/no-damage/')
                    shutil.move(file_path.replace('json', 'png'), path + 'normal/no-damage/')

            elif r_type == 'reflex_prismen':
                #Prismen does not have damages.
                    shutil.move(file_path, path + 'prismen/')
                    shutil.move(file_path.replace('json', 'png'), path + 'prismen/')
            elif r_type == 'reflex_stripe' :
                if damages == True:
                    shutil.move(file_path, path + 'stripes/damage/')
                    shutil.move(file_path.replace('json', 'png'), path + 'stripes/damage/')
                else:
                    shutil.move(file_path, path + 'stripes/no-damage/')
                    shutil.move(file_path.replace('json', 'png'), path + 'stripes/no-damage')
            else:
                #print('ERROR: No reflective bands found in the JSON')
                shutil.move(file_path, path + 'background/')
                shutil.move(file_path.replace('json', 'png'), path + 'background/')
                    
                    