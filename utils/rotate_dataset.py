import os
import argparse
import cv2
import json
import shutil
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

def rotate_point(x, y, width, height, clockwise=True):
    """Rotate a point 90 degrees."""
    if clockwise:
        return (y, width - x)
    else:
        return (height - y, x)

def process_image(img_path, output_path, clockwise):
    base = img_path.stem
    anno_path = img_path.with_suffix('.json')
    out_img = output_path / img_path.name
    out_anno = output_path / f"{base}.json"

    img = cv2.imread(str(img_path))
    if img is None:
        return
    h, w = img.shape[:2]
    should_rotate = (clockwise and h > w) or (not clockwise and w > h)

    if should_rotate:
        # Rotate the image 90 degrees
        rotated = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE if clockwise else cv2.ROTATE_90_CLOCKWISE)
        cv2.imwrite(str(out_img), rotated, [int(cv2.IMWRITE_JPEG_QUALITY), 100])

        if anno_path.exists():
            with open(anno_path, 'r', encoding='utf-8') as f:
                anno = json.load(f)
            anno['imageWidth'], anno['imageHeight'] = rotated.shape[1], rotated.shape[0]
            for shape in anno.get('shapes', []):
                # Kee p the rotation
                shape['points'] = [list(rotate_point(x, y, w, h, clockwise)) for x, y in shape.get('points', [])]
            with open(out_anno, 'w', encoding='utf-8') as f:
                json.dump(anno, f, indent=4)
    else:
        shutil.copy(str(img_path), str(out_img))
        if anno_path.exists():
            shutil.copy(str(anno_path), str(out_anno))

def check_orientation(img_path, desired_vertical):
    """Check if the image orientation matches the desired orientation."""
    img = cv2.imread(str(img_path))
    if img is None:
        return None
    h, w = img.shape[:2]
    is_vertical = h > w
    return img_path if is_vertical == desired_vertical else None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Rotate images and update LabelMe annotations.")
    parser.add_argument("direction", choices=["vertical", "horizontal"], help="Desired orientation.")
    parser.add_argument("input_folder", help="Path to input folder.")
    parser.add_argument("output_folder", nargs='?', help="Path to output folder.")
    parser.add_argument("--workers", type=int, default=8, help="Number of worker threads.")
    parser.add_argument("--check", action="store_true", help="Check image orientations without rotating.")
    args = parser.parse_args()

    clockwise = args.direction == "horizontal"
    input_path = Path(args.input_folder)
    image_exts = ('.jpg', '.jpeg', '.png', '.bmp')

    if args.check:
        with ThreadPoolExecutor(max_workers=args.workers) as executor:
            futures = []
            for img_path in input_path.rglob('*'):
                if img_path.suffix.lower() in image_exts:
                    futures.append(executor.submit(check_orientation, img_path, not clockwise))
            
            for future in futures:
                result = future.result()
                if result:
                    print(f"Image in {args.direction} orientation: {result}")
        print("Check complete.")
    else:
        if not args.output_folder:
            parser.error("output_folder is required when not using --check")
        
        output_path = Path(args.output_folder)
        output_path.mkdir(parents=True, exist_ok=True)

        with ThreadPoolExecutor(max_workers=args.workers) as executor:
            futures = []
            for img_path in input_path.rglob('*'):
                if img_path.suffix.lower() in image_exts:
                    relative_path = img_path.relative_to(input_path)
                    output_img_path = output_path / relative_path
                    output_img_path.parent.mkdir(parents=True, exist_ok=True)
                    futures.append(executor.submit(process_image, img_path, output_path, clockwise))
            
            # Wait for all tasks to complete
            for future in futures:
                future.result()

        print(f"Processing complete. Rotated images saved in {output_path}")
