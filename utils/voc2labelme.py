import numpy as np
import cv2
from imantics import Polygons, Mask
import json
import os
import argparse
from polylabel import polylabel
import imgviz

from runx.logx import logx
from config import assert_and_infer_cfg,cfg

def createJSON(label_path=None):
    input_dir = os.path.join(cfg.RESULT_DIR, 'pred/')
    out_dir = os.path.join(cfg.RESULT_DIR, 'label/')
    os.makedirs(out_dir, exist_ok=True)
    if label_path is None:
        label_list = os.path.join(cfg.DATASET.PTP_ALL_DIR, 'labels.txt')
    else:
        label_list = label_path
    print(f"The path where is gets the labels: {label_list}")
    assert os.path.exists(label_list)
    id_to_classname = {}
    for i, line in enumerate(open(label_list).readlines()):
        class_id = i - 1
        class_name = line.strip()
        if class_id == -1:
            assert class_name == "__ignore__"
            continue
        elif class_id == 0:
            assert class_name == "_background_"
        id_to_classname[class_id] = class_name

    #breakpoint()
    color_map = imgviz.label_colormap()
    gray2label = {}
    for i,color in enumerate(color_map):
        real_key = (color[0],color[1],color[2]) 
        if i in id_to_classname:
            class_name = id_to_classname[i]
        else:
            class_name = "unlabelled"
        gray2label[real_key] = class_name

    # iterate through all images
    directory = os.fsencode(input_dir)
    for idx, file in enumerate(os.listdir(directory)):
        filename = os.fsdecode(file)
        print(filename)
        filepath = input_dir + filename
        data = {}  # data to be converted to json
        data['version'] = "4.5.6"
        data["shapes"] = []
        # load image
        if not (filename.endswith(".png") or filename.endswith(".jpg")) or filename.endswith(".py"):
            continue
        img = cv2.imread(filepath)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        # split images in binary for all classes
        for lbl in gray2label:
            if not lbl == (0, 0, 0):
                data = addlabeltodata(gray2label,data, img, lbl)


        data["imagePath"] = filename.split("_pred")[0] + ".jpg"
        data["imageData"] = None
        json_pth = out_dir + filename.split("_pred")[0] + ".json"
        with open(json_pth, 'w') as outfile:
            json.dump(data, outfile, indent=4)
        if idx % 20 == 0:
            logx.msg(f'generating labels[Iter: {idx + 1} / {len(os.listdir(directory))}]')


def addlabeltodata(color2label, data, img, lbl, min_dist=25):
    # create binary mask for given label
    assert len(img.shape) == 3
    height, width, ch = img.shape
    assert ch == 3
    mask = img.copy()

    lbl_mask = np.all(img == lbl, axis=-1)

    non_lbl_mask = np.any((img != lbl), axis=-1)
    mask[lbl_mask] = [255, 255, 255]
    mask[non_lbl_mask] = [0, 0, 0]

    # mask = np.any(img != np.asarray(lbl), axis=-1)
    curr = data
    gray = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
    polygons = Mask(gray).polygons()
    point_arr = polygons.points

    for segment in point_arr:
        segment = segment.tolist()

        i = 0
        # Remove very close points from segment
        while i < len(segment):
            s = segment[i]
            to_del = []

            j = i + 1
            while j < len(segment):
                ss = segment[j]
                dist = np.sqrt(np.sum(np.square(np.array(np.array(s) - np.array(ss)))))

                if dist < min_dist:
                    to_del.append(ss)

                j += 1

            for d in to_del:
                segment.remove(d)
            i += 1

        if len(segment) >= 4:
            curr["shapes"].append({
                'label': color2label[lbl],
                'points': segment,
                "group_id": None,
                "shape_type": "polygon",
                "flags": {}
            })
    return curr

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Semantic Segmentation')
    parser.add_argument('--result_dir', type=str, default='./logs',
                    help='where to write log output')
    parser.add_argument("--label_list", type=str, required=False)
    parser.add_argument('--global_rank', default=0, type=int,
                    help='parameter used by apex library')
    args = parser.parse_args()
    #assert_and_infer_cfg(args)
    cfg.RESULT_DIR = args.result_dir
    logx.initialize(logdir=args.result_dir,
                    tensorboard=True, hparams=vars(args),
                    global_rank=args.global_rank)
    if args.label_list:
        label_path = args.label_list
    createJSON(label_path=label_path)