#!/usr/bin/env python3
"""
check_pairs.py

Description:
    This script operates in two modes: 'folders' and 'files'.

    - In 'folders' mode, it identifies and moves files with unique base names from two source folders to a destination folder.
      A base name is considered unique if it does not exist in both source folders, regardless of file extensions.

    - In 'files' mode, it checks that inside a folder, each base name has files with all specified extensions.
      If any base name is missing files with any of the required extensions, it moves those files to an output folder.

Usage:
    python check_pairs.py folders folder1 folder2 folder_to_move
    python check_pairs.py files input_folder output_folder --extensions ext1 ext2 ...

Examples:
    Folders mode:
        python check_pairs.py folders /path/to/folder1 /path/to/folder2 /path/to/output_folder

    Files mode:
        python check_pairs.py files /path/to/input_folder /path/to/output_folder --extensions jpg json

"""

import argparse
from pathlib import Path
import shutil
import sys

def check_pairs(folder1: Path, folder2: Path, folder_to_move: Path) -> None:
    """
    Moves files with unique base names from folder1 and folder2 to folder_to_move.

    Args:
        folder1 (Path): Path to the first source folder.
        folder2 (Path): Path to the second source folder.
        folder_to_move (Path): Path to the destination folder where unique files will be moved.

    Raises:
        FileNotFoundError: If folder1 or folder2 does not exist or is not a directory.
        Exception: If an error occurs during file movement.
    """
    # Validate that folder1 and folder2 exist and are directories
    for folder in [folder1, folder2]:
        if not folder.is_dir():
            print(f"Error: '{folder}' is not a valid directory.", file=sys.stderr)
            sys.exit(1)

    # Create the destination folder if it doesn't exist
    folder_to_move.mkdir(parents=True, exist_ok=True)

    # Extract base names (without extensions) from both folders
    bases1 = {file.stem for file in folder1.iterdir() if file.is_file()}
    bases2 = {file.stem for file in folder2.iterdir() if file.is_file()}

    # Identify unique base names in each folder
    unique1, unique2 = bases1 - bases2, bases2 - bases1

    # Function to move files with unique base names
    def move_unique_files(source: Path, unique_bases: set):
        for base in unique_bases:
            for file in source.glob(f"{base}.*"):
                try:
                    shutil.move(str(file), folder_to_move)
                    print(f"Moved: {file} -> {folder_to_move}")
                except Exception as e:
                    print(f"Error moving {file}: {e}", file=sys.stderr)

    # Move unique files from both folders
    move_unique_files(folder1, unique1)
    move_unique_files(folder2, unique2)

    print(f"Unique files have been moved to '{folder_to_move}'.")

def check_file_pairs(input_folder: Path, output_folder: Path, extensions: list) -> None:
    """
    Checks for files with specified extensions that are missing pairs and moves them to output_folder.

    Args:
        input_folder (Path): Path to the input folder.
        output_folder (Path): Path to the output folder where unmatched files will be moved.
        extensions (list): List of extensions to check for each base name.

    Raises:
        FileNotFoundError: If input_folder does not exist or is not a directory.
        Exception: If an error occurs during file movement.
    """
    # Validate that input_folder exists and is a directory
    if not input_folder.is_dir():
        print(f"Error: '{input_folder}' is not a valid directory.", file=sys.stderr)
        sys.exit(1)
    
    # Create the output folder if it doesn't exist
    output_folder.mkdir(parents=True, exist_ok=True)

    # Normalize extensions: remove leading dots and make lower case
    normalized_extensions = {ext.lstrip('.').lower() for ext in extensions}

    # Build a mapping from base names to extensions present
    files_in_folder = [file for file in input_folder.iterdir() if file.is_file()]
    base_to_exts = {}
    for file in files_in_folder:
        base_name = file.stem
        ext = file.suffix.lstrip('.').lower()
        if base_name not in base_to_exts:
            base_to_exts[base_name] = set()
        base_to_exts[base_name].add(ext)
    
    # For each base name, check if all required extensions are present
    for base_name, exts_present in base_to_exts.items():
        if not normalized_extensions.issubset(exts_present):
            # Missing some required extensions, move the files with this base name to output_folder
            for ext in exts_present:
                file_to_move = input_folder / f"{base_name}.{ext}"
                try:
                    shutil.move(str(file_to_move), output_folder)
                    print(f"Moved: {file_to_move} -> {output_folder}")
                except Exception as e:
                    print(f"Error moving {file_to_move}: {e}", file=sys.stderr)
    print(f"Unmatched files have been moved to '{output_folder}'.")

def main():
    """Parses command-line arguments and executes the appropriate function."""
    parser = argparse.ArgumentParser(
        description="Move files based on base name uniqueness or missing pairs."
    )
    parser.add_argument("operation", choices=["folders", "files"],
                        help="Operation mode: 'folders' or 'files'.")
    parser.add_argument("folder1", type=Path, help="Path to the first source folder (or input folder in 'files' mode).")
    parser.add_argument("folder2_or_output_folder", type=Path,
                        help="Path to the second source folder or output folder.")
    parser.add_argument("folder_to_move", nargs='?', type=Path,
                        help="Path to the destination folder (only in 'folders' mode).")
    parser.add_argument("--extensions", nargs='+', help="List of extensions to check for pairs in 'files' mode.")

    args = parser.parse_args()

    if args.operation == 'folders':
        # Ensure folder_to_move is provided
        if args.folder_to_move is None:
            print("Error: 'folder_to_move' argument is required in 'folders' mode.", file=sys.stderr)
            sys.exit(1)
        try:
            check_pairs(args.folder1, args.folder2_or_output_folder, args.folder_to_move)
        except Exception as e:
            print(f"An error occurred: {e}", file=sys.stderr)
            sys.exit(1)
    elif args.operation == 'files':
        # Ensure extensions are provided
        if not args.extensions:
            print("Error: '--extensions' argument is required in 'files' mode.", file=sys.stderr)
            sys.exit(1)
        try:
            check_file_pairs(args.folder1, args.folder2_or_output_folder, args.extensions)
        except Exception as e:
            print(f"An error occurred: {e}", file=sys.stderr)
            sys.exit(1)

if __name__ == "__main__":
    main()

