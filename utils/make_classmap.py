#!/usr/bin/env python3
import argparse
import os
import json
import random
import re

def get_unique_random_color(used_colors):
    while True:
        color = [random.randint(0, 255) for _ in range(3)]
        t_color = tuple(color)
        if t_color not in used_colors:
            used_colors.add(t_color)
            return color

def quote_key(key):
    # Quote key if it contains characters other than alphanumerics, underscores, or hyphens.
    if re.match(r'^[a-zA-Z0-9_-]+$', key):
        return key
    return f'"{key}"'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input_path", help="Directory containing JSON annotation files (and images).")
    parser.add_argument("output_path", help="Directory to write classmap.yaml.")
    args = parser.parse_args()

    all_labels = set()
    for root, _, files in os.walk(args.input_path):
        for file in files:
            if file.lower().endswith(".json"):
                json_path = os.path.join(root, file)
                try:
                    with open(json_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    for shape in data.get("shapes", []):
                        label = shape.get("label", "").strip().lower()
                        if label:
                            all_labels.add(label)
                except Exception as e:
                    print(f"Could not process file {json_path}: {e}")

    classmap = {}
    used_colors = set()
    for label in sorted(all_labels):
        # Replace spaces with underscores.
        key = label.replace(" ", "_")
        color = get_unique_random_color(used_colors)
        classmap[key] = [color, "defect", 50, False, 0.4]

    os.makedirs(args.output_path, exist_ok=True)
    output_file = os.path.join(args.output_path, "classmap.yaml")
    with open(output_file, "w", encoding="utf-8") as f:
        for key, value in classmap.items():
            key_str = quote_key(key)
            # Use json.dumps for inline list formatting.
            value_str = json.dumps(value, separators=(", ", ", "))
            f.write(f"{key_str}: {value_str}\n")

    print(f"Classmap saved to: {output_file}")

if __name__ == "__main__":
    main()
