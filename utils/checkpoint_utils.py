import glob
import os
import torch

def get_last_full_stats(checkpoint_dir):
    """
    Returns (epoch, mean_iu) of the most recent checkpoint whose filename
    contains 'full'.  Falls back to (-1, 0.0) if none exist.
    """
    ckpts = sorted(glob.glob(os.path.join(checkpoint_dir, '*full*.pth')))
    if not ckpts:
        return -1, 0.0
    ckpt = torch.load(ckpts[-1], map_location='cpu')
    return ckpt.get('epoch', -1), ckpt.get('mean_iu', 0.0)
