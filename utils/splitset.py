import os
import argparse
import random
import shutil
import glob
from tqdm import tqdm

# python helper/splitset.py data/ptp/JPEGImages data/ptp/SegmentationClassGray data/ptp_split
def main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("img_dir", help="path to img directory")
    parser.add_argument("mask_dir", help="path to mask directory")
    parser.add_argument("out_dir", help="path to out directory")
    args = parser.parse_args()

    # Create output directories if they don't exist
    os.makedirs(args.out_dir, exist_ok=True)

    dest_train = os.path.join(args.out_dir, "train/")
    dest_val = os.path.join(args.out_dir, "val/")
    dest_test = os.path.join(args.out_dir, "test/")

    train_images = os.path.join(dest_train, "images/")
    train_seg = os.path.join(dest_train, "seg/")
    val_images = os.path.join(dest_val, "images/")
    val_seg = os.path.join(dest_val, "seg/")
    test_images = os.path.join(dest_test, "images/")
    test_seg = os.path.join(dest_test, "seg/")

    for path in [dest_train, dest_val, dest_test, train_images, train_seg, val_images, val_seg, test_images, test_seg]:
        os.makedirs(path, exist_ok=True)

    files = glob.glob(os.path.join(args.mask_dir, "*.png"))
    random.shuffle(files)

    print(f"Total mask files found: {len(files)}")

    train = files[:(int(len(files)*0.95))]
    val = files[(int(len(files)*0.95)):]
    test = files[(int(len(files)*0.95)):]

    def copy_if_exists(mask_file, img_dir, seg_dest, img_dest):
        base_name = os.path.basename(mask_file)
        img_name = base_name.replace(".png", ".jpg")
        img_path = os.path.join(img_dir, img_name)

        if os.path.exists(mask_file) and os.path.exists(img_path):
            shutil.copyfile(mask_file, os.path.join(seg_dest, base_name))
            shutil.copyfile(img_path, os.path.join(img_dest, img_name))

    print("Creating train folder..")
    for i in tqdm(train):
        copy_if_exists(i, args.img_dir, train_seg, train_images)

    print("Creating val folder..")
    for j in tqdm(val):
        copy_if_exists(j, args.img_dir, val_seg, val_images)

    print("Creating test folder..")
    for k in tqdm(test):
        copy_if_exists(k, args.img_dir, test_seg, test_images)

if __name__ == '__main__':
    main()
