import os
import argparse
import random
import shutil
import glob
from tqdm import tqdm
import json

# Set your default paths here (edit as needed)
DEFAULT_IMG_DIR = "/home/<USER>/Documents/defis-medical/"
DEFAULT_MASK_DIR = "/home/<USER>/Documents/defis-medical/images/seg"
DEFAULT_OUT_DIR = "/home/<USER>/Documents/defis-medical/images/split"

# python helper/splitset.py data/ptp/JPEGImages data/ptp/SegmentationClassGray data/ptp_split
def main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument("--img_dir", type=str, default=DEFAULT_IMG_DIR, help="path to img directory")
    parser.add_argument("--mask_dir", type=str, default=DEFAULT_MASK_DIR, help="path to mask directory")
    parser.add_argument("--out_dir", type=str, default=DEFAULT_OUT_DIR, help="path to out directory")
    # Allow positional arguments for backward compatibility
    parser.add_argument("img_dir_pos", nargs="?", default=None)
    parser.add_argument("mask_dir_pos", nargs="?", default=None)
    parser.add_argument("out_dir_pos", nargs="?", default=None)
    args = parser.parse_args()

    # Prefer --img_dir etc, but fallback to positional if not set
    img_dir = args.img_dir or args.img_dir_pos
    mask_dir = args.mask_dir or args.mask_dir_pos
    out_dir = args.out_dir or args.out_dir_pos

    if not img_dir or not mask_dir or not out_dir:
        print("[ERROR] Please provide all required directories (img_dir, mask_dir, out_dir).")
        return

    # Create output directories if they don't exist
    os.makedirs(out_dir, exist_ok=True)

    dest_train = os.path.join(out_dir, "train/")
    dest_val = os.path.join(out_dir, "val/")
    dest_test = os.path.join(out_dir, "test/")

    train_images = os.path.join(dest_train, "images/")
    train_seg = os.path.join(dest_train, "seg/")
    val_images = os.path.join(dest_val, "images/")
    val_seg = os.path.join(dest_val, "seg/")
    test_images = os.path.join(dest_test, "images/")
    test_seg = os.path.join(dest_test, "seg/")

    for path in [dest_train, dest_val, dest_test, train_images, train_seg, val_images, val_seg, test_images, test_seg]:
        os.makedirs(path, exist_ok=True)

    files = glob.glob(os.path.join(mask_dir, "*.png"))
    random.shuffle(files)

    print(f"Total mask files found: {len(files)}")

    train = files[:(int(len(files)*0.95))]
    val = files[(int(len(files)*0.95)):]
    test = files[(int(len(files)*0.95)):]

    # List of defect classes to group by (update as needed)
    DEFECT_CLASSES = [
        "hole", "strips", "stain", "line", "knots", "fiber", "surface", "elastic",
        "pull", "seam", "band", "pilling", "knot", "platine", "colouring fold"
    ]


    # Aliases for label normalization
    LABEL_ALIASES = {
        "strip": "strips",
        "strips": "strips",
        "stain": "stain",
        "pull": "pull",
        "knot": "knots",
        "knots": "knots",
        "seam": "seam",
        # Add more aliases as needed
    }

    def normalize_label(label):
        """Normalize label using LABEL_ALIASES and lowercasing."""
        label = label.lower().strip()
        return LABEL_ALIASES.get(label, label)

    def get_defect_from_json(mask_file, mask_dir):
        """
        Given a mask file, look for a corresponding JSON file in the same directory.
        Return the first defect class found in the JSON annotation, or None if not found.
        """
        base_name = os.path.splitext(os.path.basename(mask_file))[0]
        json_path = os.path.join(mask_dir, base_name + ".json")
        if not os.path.exists(json_path):
            print(f"[LOG] JSON not found for {mask_file}: {json_path}")
            return None
        try:
            with open(json_path, "r") as f:
                data = json.load(f)
            # Try to find a defect class in the annotation
            if "shapes" in data:
                for shape in data["shapes"]:
                    label = shape.get("label", "")
                    norm_label = normalize_label(label)
                    print(f"[LOG] {mask_file} - found label: '{label}' (normalized: '{norm_label}')")
                    if norm_label in DEFECT_CLASSES:
                        print(f"[LOG] {mask_file} - defect class matched: '{norm_label}'")
                        return norm_label
            else:
                print(f"[LOG] {mask_file} - no 'shapes' in JSON")
        except Exception as e:
            print(f"[ERROR] Exception reading/parsing {json_path}: {e}")
        return None

    def copy_if_exists(mask_file, img_dir, seg_dest, img_dest, mask_dir):
        base_name = os.path.basename(mask_file)
        img_name = base_name.replace(".png", ".jpg")
        img_path = os.path.join(img_dir, img_name)

        defect = get_defect_from_json(mask_file, mask_dir)
        print(f"[LOG] Processing {mask_file} | defect: {defect}")

        if defect:
            seg_dest = os.path.join(seg_dest, defect)
            img_dest = os.path.join(img_dest, defect)
            os.makedirs(seg_dest, exist_ok=True)
            os.makedirs(img_dest, exist_ok=True)
            print(f"[LOG] Saving to {seg_dest} and {img_dest}")

        if os.path.exists(mask_file) and os.path.exists(img_path):
            shutil.copyfile(mask_file, os.path.join(seg_dest, base_name))
            shutil.copyfile(img_path, os.path.join(img_dest, img_name))
            print(f"[LOG] Copied {mask_file} and {img_path} to {seg_dest} and {img_dest}")
        else:
            if not os.path.exists(mask_file):
                print(f"[WARN] Mask file does not exist: {mask_file}")
            if not os.path.exists(img_path):
                print(f"[WARN] Image file does not exist: {img_path}")

    print("Creating train folder..")
    for i in tqdm(train):
        copy_if_exists(i, img_dir, train_seg, train_images, mask_dir)

    print("Creating val folder..")
    for j in tqdm(val):
        copy_if_exists(j, img_dir, val_seg, val_images, mask_dir)

    print("Creating test folder..")
    for k in tqdm(test):
        copy_if_exists(k, img_dir, test_seg, test_images, mask_dir)

if __name__ == '__main__':
    main()
