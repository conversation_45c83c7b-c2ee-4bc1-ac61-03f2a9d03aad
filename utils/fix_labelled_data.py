import os
import json
import argparse

def process_json_files(source_folder, destination_folder):
    for filename in os.listdir(source_folder):
        if filename.endswith(".json"):
            source_file_path = os.path.join(source_folder, filename)
            destination_file_path = os.path.join(destination_folder, filename)

            with open(source_file_path, 'r') as file:
                data = json.load(file)

                # Modify the label to lowercase
                for shape in data['shapes']:
                    shape['label'] = shape['label'].lower()
                    if shape['label'] == "damage":
                        shape['label'] = "damaged"


                # Empty the imageData field
                data['imageData'] = None

            with open(destination_file_path, 'w') as file:
                json.dump(data, file, indent=4)

def main():
    parser = argparse.ArgumentParser(description="Process JSON files.")
    parser.add_argument("source_folder", help="Path to the source folder containing JSON files")
    parser.add_argument("destination_folder", help="Path to the destination folder for processed files")
    args = parser.parse_args()

    os.makedirs(args.destination_folder, exist_ok=True)
    process_json_files(args.source_folder, args.destination_folder)

if __name__ == "__main__":
    main()
