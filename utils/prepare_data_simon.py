"""
# File: prepare_data.py
# Author: <PERSON><PERSON>
# Copyright: Desion GmbH
# Description:
    The process for being able to train HRNET with new data contained quite a few steps that were bothersome to execute each time. This script summarizes these.
    It includes:
    -fixing the order of the labels in the json
    -converting the json label to PNG segmentation label
    -converting these to the class values (Greyscale)

# Changelog:
    2024-10-10: Optimized "process_json_file" to use concurrency
"""

import argparse
import concurrent.futures
import glob
import json
import os
import shutil

import cv2
import imgviz
import labelme
import numpy as np
from tqdm import tqdm

from utils.data_converter import yaml_file_to_dict, json_file_to_dict, dict_to_json_file
import multiprocessing


def fix_label_order(path, classmap, verbose=True, num_workers=8):
    """
    Fix the order of labels in JSON annotation files by following the ordering specified in class_map.yaml.
    If a shape's label is not found in the classmap, an error is thrown with the JSON file path included.

    :param path: Input directory containing JSON files.
    :param classmap: Ordered dictionary mapping labels to their details (color, type, etc.).
    :param verbose: Flag to enable/disable verbose output (default: True).
    :param num_workers: Number of worker threads to use (default: 8).
    """

    # Extract the order from the classmap keys (order as defined in the YAML)
    ordered_labels = list(classmap.keys())

    def reorder_file(ann_file, path, ordered_labels):
        input_file = os.path.join(path, ann_file)
        try:
            anno = json_file_to_dict(input_file)

            # Define a helper function that includes the file path in the error message.
            def get_label_index(label):
                if label not in ordered_labels:
                    raise ValueError(f"Label '{label}' not found in the ordered labels from the classmap. File: {input_file}")
                return ordered_labels.index(label)

            # Sort shapes using the helper function.
            anno['shapes'] = sorted(anno['shapes'], key=lambda x: get_label_index(x['label']))
            dict_to_json_file(anno, input_file)
        except Exception as e:
            print(f"Error processing {input_file}: {e}")

    json_files = [f for f in os.listdir(path) if f.endswith(".json")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(reorder_file, file, path, ordered_labels) for file in json_files]

        for result in tqdm(concurrent.futures.as_completed(futures), total=len(json_files),
                           desc="Processing files", disable=not verbose):
            result.result()

    if verbose:
        print("Label order fixing completed. Original files have been updated.")


def process_json_file(filename, image_dir, seg_dir, class_name_to_id, labelme_dir, no_compress=False):
    base = os.path.splitext(os.path.basename(filename))[0]
    out_img_file = os.path.join(image_dir, f"{base}.jpg")
    out_png_file = os.path.join(seg_dir, f"{base}.png")

    try:
        # Attempt to process the label file
        process_label_file(filename, out_img_file, out_png_file, class_name_to_id, no_compress)
    except Exception as e:
        # If an error occurs, fix the annotation file
        fix_annotation_file(filename)
        try:
            # Retry processing after fixing
            process_label_file(filename, out_img_file, out_png_file, class_name_to_id, no_compress)
        except Exception as e:
            # If it still fails, move files to 'labelme_tofix' directory
            print(f"Problem with file {filename}: {str(e)}")
            tofix_dir = os.path.join(os.path.dirname(labelme_dir), "labelme_tofix")
            os.makedirs(tofix_dir, exist_ok=True)
            for ext in [".json", ".jpg", ".png"]:
                src = os.path.join(labelme_dir, f"{base}{ext}")
                if os.path.exists(src):
                    shutil.move(src, os.path.join(tofix_dir, f"{base}{ext}"))


def process_label_file(filename, out_img_file, out_png_file, class_name_to_id, no_compress=False):
    # Load the label file
    label_file = labelme.LabelFile(filename)
    
    # Save the image data to a file with optional compression
    with open(out_img_file, "wb") as f:
        if no_compress:
            f.write(label_file.imageData)
        else:
            # Convert to numpy array and save with compression
            img_array = labelme.utils.img_data_to_arr(label_file.imageData)
            success = cv2.imwrite(out_img_file, cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR))
            if not success:
                raise RuntimeError(f"Failed to save compressed image: {out_img_file}")

    # Convert image data to an array
    img = labelme.utils.img_data_to_arr(label_file.imageData)
    # Generate the label image
    lbl, _ = labelme.utils.shapes_to_label(
        img.shape, label_file.shapes, class_name_to_id
    )
    # Save the label image
    labelme.utils.lblsave(out_png_file, lbl)


def fix_annotation_file(filename):
    # Ensure a polygon has at least 3 points.

    # Open the JSON file and load the data
    data = json_file_to_dict(filename)

    # Remove shapes with less than 3 points
    data["shapes"] = [s for s in data["shapes"] if len(s["points"]) > 2]

    # Fix shapes that have exactly 2 points
    dict_to_json_file(data,filename)


def json_to_seg_image(labelme_dir, seg_dir, classmap, num_workers=8, no_compress=False):
    os.makedirs(seg_dir, exist_ok=True)

    class_names = list(classmap.keys())
    assert class_names[0].replace("_","") ==  "background", "Class 'background' must be the class [0]"

    class_name_to_id = {name: i for i, name in enumerate(class_names)}

    print("class_names:", class_names)

    json_files = glob.glob(os.path.join(labelme_dir, "*.json"))
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [
            executor.submit(process_json_file, filename, labelme_dir, seg_dir, class_name_to_id, labelme_dir, no_compress)
            for filename in json_files
        ]

        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(json_files), desc="Processing JSON files"):
            pass


def colours_to_gray_seg(input_path, output_path, color2index, num_workers=8):
    os.makedirs(output_path, exist_ok=True)
    
    # Create a 256x256x256 lookup table
    lut = np.zeros((256, 256, 256), dtype=np.uint8)
    for color, index in color2index.items():
        lut[color[0], color[1], color[2]] = index

    def process_image(file):
        if not file.endswith(".png"):
            return
        try:
            # Read the image
            im = cv2.imread(os.path.join(input_path, file))
            if im is None:
                raise ValueError(f"Failed to read image: {file}")
            
            # Convert to RGB
            im_rgb = cv2.cvtColor(im, cv2.COLOR_BGR2RGB)
            
            # Apply the LUT
            new_im = lut[im_rgb[:,:,0], im_rgb[:,:,1], im_rgb[:,:,2]]
            
            # Save the result
            cv2.imwrite(os.path.join(output_path, file), new_im)
        except Exception as e:
            print(f"Failed processing {file}: {str(e)}")

    files = [f for f in os.listdir(input_path) if f.endswith(".png")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(process_image, file) for file in files]

        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(files), desc="Processing images"):
            pass


def validate_gray_seg(seg_dir, output_dir, color_map, num_workers=8):
    os.makedirs(output_dir, exist_ok=True)

    def process_image(file):
        if not file.endswith(".png"):
            return
        try:
            # Read the grayscale image
            gray_img = cv2.imread(os.path.join(seg_dir, file), cv2.IMREAD_GRAYSCALE)
            if gray_img is None:
                raise ValueError(f"Failed to read image: {file}")
            
            # Create a colored image
            colored_img = np.zeros((gray_img.shape[0], gray_img.shape[1], 3), dtype=np.uint8)
            for idx, color in enumerate(color_map):
                colored_img[gray_img == idx] = color
            
            # Save the colored image
            cv2.imwrite(os.path.join(output_dir, file), cv2.cvtColor(colored_img, cv2.COLOR_RGB2BGR))
        except Exception as e:
            print(f"Failed processing {file}: {str(e)}")

    files = [f for f in os.listdir(seg_dir) if f.endswith(".png")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(process_image, file) for file in files]

        for _ in tqdm(concurrent.futures.as_completed(futures), total=len(files), desc="Validating images"):
            pass


if __name__ == '__main__':
    """
    Prepare data for training HRNET by processing JSON files and generating segmentation images.

    This function performs the following steps:
    1. Fix label order in JSON files
    2. Convert JSON files to segmentation images
    3. Convert color segmentation images to grayscale

    :param root_dir: Root folder containing the labelme folder with images and JSON files
    :param classmap: Path to classmap file (YAML format)
    :param start_step: Starting step (0: Fix label order, 1: JSON to seg, 2: Colors to gray)
    :param not_verbose: Flag to disable verbose output
    """
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("root_dir", help="Root folder containing the labelme folder with images and JSON files")
    parser.add_argument("--start_step", default=0, type=int,
            help="0: Fix label order, 1: json to seg, 2: colours to gray, 3: Perform a validation")
    parser.add_argument("--not-verbose", action="store_true", help="Disable verbose output")
    parser.add_argument("--validate", action="store_true", help="Validate gray images after processing")
    parser.add_argument("--no-compress", action="store_true", help="Disable image compression when saving")
    args = parser.parse_args()

    # Set paths
    image_dir = os.path.join(args.root_dir, "images")
    output_seg = os.path.join(args.root_dir, "seg")
    output_seg_colour = os.path.join(args.root_dir, "seg_colour")
    classmap_path = "/mnt/data/projects/hrnet/configs/nips/class_map.yaml"
    classmap = yaml_file_to_dict(classmap_path)
    color_map = imgviz.label_colormap()
    available_threads = multiprocessing.cpu_count()

    verbose = not args.not_verbose

    if args.start_step == 0:
        print("Step 0. Fixing order: 1) Garments, 2) Stripes 3) Defects")

        fix_label_order(image_dir, classmap, verbose)
        args.start_step += 1

    if args.start_step == 1:
        os.makedirs(output_seg, exist_ok=True)
        print("Step 1. Generating color image annotations")

        json_to_seg_image(image_dir, output_seg_colour, classmap, num_workers=available_threads, no_compress=args.no_compress)
        args.start_step += 1

    if args.start_step == 2:

        color2index = {}
        for i,color in enumerate(color_map):
            real_key = (color[0],color[1],color[2])
            color2index[real_key] = i

        print("Step 2. Generating gray image annotations")
        colours_to_gray_seg(output_seg_colour, output_seg, color2index, num_workers=available_threads)

        if args.validate:
            args.start_step += 1

    if args.start_step == 3:
        print("Step 3. Validating grayscale images")

        output_seg_validation = os.path.join(args.root_dir, "seg_validation")
        validate_gray_seg(output_seg, output_seg_validation, color_map, num_workers=args.workers)

        print("Validation complete. Please compare the images in 'seg_colour' and 'seg_validation' folders.")
        print("They should be identical if the conversion process was successful.")
