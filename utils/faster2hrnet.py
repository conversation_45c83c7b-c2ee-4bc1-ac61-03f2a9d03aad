import argparse
import os
import xml.etree.ElementTree as ET
import cv2
import numpy as np
import json

def main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("--input_dir", help="input annotated directory")
    parser.add_argument("--image_dir", help="input image directory")
    parser.add_argument("--image_ext", default=".PNG", help="input image directory")
    parser.add_argument("--output_dir", help="output dataset directory")
    parser.add_argument("--label_file", help="label map")

    args = parser.parse_args()

    with open(args.label_file,"r") as jsonfile:
        labelmap = json.load(jsonfile)
    labellist = list(reversed(list(labelmap.keys())))

    os.makedirs(args.output_dir,exist_ok=True)
    output_path_images = args.output_dir#os.path.join(args.output_dir, "JPEGImages")
    os.makedirs(output_path_images,exist_ok=True)
    output_path_labels = args.output_dir#os.path.join(args.output_dir, "SegmentationClassPNG")
    os.makedirs(output_path_labels,exist_ok=True)

    for annofile in os.listdir(args.input_dir):
        if not "-" in annofile:
            continue
        output_filename = annofile.replace(".xml",".png")
        input_imagename = annofile.replace(".xml",args.image_ext)
        output_imagename = annofile.replace(".xml",".jpg")
        input_imagepath = os.path.join(args.image_dir,input_imagename)
        if not os.path.isfile(input_imagepath):
            continue
        try:
            print(input_imagepath)
            input_image = cv2.imread(input_imagepath)
            imgshape =  input_image.shape[:2]
            cv2.imwrite(os.path.join(output_path_images,output_imagename),input_image)
            annotree = ET.parse(os.path.join(args.input_dir,annofile))
            annoroot = annotree.getroot()
            labels = {}
            for child in annoroot:
                #if child.tag == "size":
                #    imgshape = (int(child[1].text), int(child[0].text))
                if child.tag == "object":
                    labelclass = child[0].text
                    #bb = (int(child[4][0].text),int(child[4][1].text),int(child[4][2].text),int(child[4][3].text))
                    bb = [(int(child[4][0].text),int(child[4][1].text)), (int(child[4][2].text),int(child[4][3].text))]
                    if not labelclass in labels:
                        labels[labelclass] = []
                    labels[labelclass].append(bb)
            output_file = np.zeros(imgshape)
            for label in labellist:
                if not label in labels:
                    continue
                labelvalue = labelmap[label]
                bbs = labels[label]
                for bb in bbs:
                    cv2.rectangle(output_file,bb[0],bb[1],labelvalue,-1)
            cv2.imwrite(os.path.join(output_path_labels,output_filename),output_file)
        except:
            print("Skipped {}".format(input_imagepath))

                




if __name__ == "__main__":
    main()
