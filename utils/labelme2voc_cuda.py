#!/usr/bin/env python

from __future__ import print_function
import argparse
import glob
import os
import os.path as osp
import sys
import numpy as np
import labelme
import imgviz
import torch
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm

def process_file(filename, args, class_name_to_id, class_names, device):
    try:
        # Load label file
        label_file = labelme.LabelFile(filename=filename)

        base = osp.splitext(osp.basename(filename))[0]
        out_img_file = osp.join(args.output_dir, "JPEGImages", base + ".jpg")
        out_lbl_file = osp.join(args.output_dir, "SegmentationClass", base + ".npy")
        out_png_file = osp.join(args.output_dir, "SegmentationClassPNG", base + ".png")
        if not args.noviz:
            out_viz_file = osp.join(args.output_dir, "SegmentationClassVisualization", base + ".jpg")

        # Save image data
        with open(out_img_file, "wb") as f:
            f.write(label_file.imageData)

        # Convert image data to array and move to GPU
        img = labelme.utils.img_data_to_arr(label_file.imageData)
        img_tensor = torch.from_numpy(img).to(device)

        # Ensure img_shape is (height, width)
        img_shape = img_tensor.shape[:2]  # Corrected from shape[1:]

        # Debugging: Print shapes
        # print(f"Image shape: {img.shape}")  # (H, W, C)
        # print(f"Image tensor shape: {img_tensor.shape}")  # (H, W, C)

        # Create label using CPU (labelme might not support GPU tensors)
        lbl, _ = labelme.utils.shapes_to_label(
            img_shape=img_shape,  # Corrected
            shapes=label_file.shapes,
            label_name_to_value=class_name_to_id,
        )
        lbl_tensor = torch.from_numpy(lbl).to(device)

        # Save label as PNG and NPY
        lbl_cpu = lbl_tensor.cpu().numpy()
        labelme.utils.lblsave(out_png_file, lbl_cpu)
        np.save(out_lbl_file, lbl_cpu)

        # If visualization is enabled, create and save visualization
        if not args.noviz:
            # Convert tensors back to CPU for imgviz
            img_cpu = img_tensor.cpu().numpy()
            lbl_cpu = lbl_tensor.cpu().numpy()

            # Ensure image and label dimensions match
            assert img_cpu.shape[:2] == lbl_cpu.shape, f"Mismatch in image and label shapes: {img_cpu.shape[:2]} vs {lbl_cpu.shape}"

            # Create visualization
            viz = imgviz.label2rgb(
                label=lbl_cpu,
                image=imgviz.rgb2gray(img_cpu),
                font_size=15,
                label_names=class_names,
                loc="rb",
            )
            imgviz.io.imsave(out_viz_file, viz)

    except Exception as ex:
        print(f"Error processing {filename}: {ex}")

def main():
    parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("input_dir", help="input annotated directory")
    parser.add_argument("output_dir", help="output dataset directory")
    parser.add_argument("--labels", help="labels file", required=True)
    parser.add_argument("--noviz", help="no visualization", action="store_true")
    parser.add_argument("--workers", help="number of worker threads", type=int, default=8)
    args = parser.parse_args()

    if osp.exists(args.output_dir):
        print("Output directory already exists:", args.output_dir)
        sys.exit(1)
    os.makedirs(args.output_dir)
    os.makedirs(osp.join(args.output_dir, "JPEGImages"))
    os.makedirs(osp.join(args.output_dir, "SegmentationClass"))
    os.makedirs(osp.join(args.output_dir, "SegmentationClassPNG"))
    if not args.noviz:
        os.makedirs(osp.join(args.output_dir, "SegmentationClassVisualization"))
    print("Creating dataset:", args.output_dir)

    # Load class names and create mapping
    class_names = []
    class_name_to_id = {}
    with open(args.labels, "r") as f:
        for i, line in enumerate(f):
            class_id = i - 1  # starts with -1
            class_name = line.strip()
            class_name_to_id[class_name] = class_id
            if class_id == -1:
                assert class_name == "__ignore__", "First label must be __ignore__"
                continue
            elif class_id == 0:
                assert class_name == "_background_", "Second label must be _background_"
            class_names.append(class_name)
    class_names = tuple(class_names)
    print("class_names:", class_names)
    
    # Save class names to a file
    out_class_names_file = osp.join(args.output_dir, "class_names.txt")
    with open(out_class_names_file, "w") as f:
        f.writelines("\n".join(class_names))
    print("Saved class_names:", out_class_names_file)

    # Get list of JSON files
    json_files = glob.glob(osp.join(args.input_dir, "*.json"))
    total_files = len(json_files)
    print(f"Total JSON files to process: {total_files}")

    # Set up CUDA device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    if torch.cuda.is_available():
        print(f"Using CUDA device: {torch.cuda.get_device_name(device)}")
    else:
        print("CUDA is not available. Using CPU.")

    # Process files with ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=args.workers) as executor:
        futures = {
            executor.submit(process_file, filename, args, class_name_to_id, class_names, device): filename
            for filename in json_files
        }
        for future in tqdm(as_completed(futures), total=total_files, desc="Processing files"):
            try:
                future.result()
            except Exception as ex:
                print(f"Error processing {futures[future]}: {ex}")

    print("Processing complete.")

if __name__ == "__main__":
    main()
