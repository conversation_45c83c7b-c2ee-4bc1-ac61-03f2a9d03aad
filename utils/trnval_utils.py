"""
Copyright 2020 Nvidia Corporation

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
"""
import os
import time

import numpy as np
import torch
import torch.nn.functional as F

from config import cfg
from utils.misc import fast_hist, fmt_scale
from utils.misc import AverageMeter, eval_metrics
from utils.misc import metrics_per_image
import cv2

from runx.logx import logx


def flip_tensor(x, dim):
    """
    Flip Tensor along a dimension
    """
    dim = x.dim() + dim if dim < 0 else dim
    return x[tuple(slice(None, None) if i != dim
                   else torch.arange(x.size(i) - 1, -1, -1).long()
                   for i in range(x.dim()))]


def resize_tensor(inputs, target_size):
    inputs = torch.nn.functional.interpolate(
        inputs, size=target_size, mode='bilinear',
        align_corners=cfg.MODEL.ALIGN_CORNERS)
    return inputs


def calc_err_mask(pred, gtruth, num_classes, classid):
    """
    calculate class-specific error masks
    """
    # Class-specific error mask
    class_mask = (gtruth >= 0) & (gtruth == classid)
    fp = (pred == classid) & ~class_mask & (gtruth != cfg.DATASET.IGNORE_LABEL)
    fn = (pred != classid) & class_mask
    err_mask = fp | fn

    return err_mask.astype(int)


def calc_err_mask_all(pred, gtruth, num_classes):
    """
    calculate class-agnostic error masks
    """
    # Class-specific error mask
    mask = (gtruth >= 0) & (gtruth != cfg.DATASET.IGNORE_LABEL)
    err_mask = mask & (pred != gtruth)

    return err_mask.astype(int)

def modify_prediction_mask(prediction, center_percentage=0.2):
    """
    Given a prediction mask (2D numpy array) with integer labels,
    finds the largest continuous (connected) area that is at least
    partially within the central region of the image. The central region
    is defined as a box covering center_percentage of the image dimensions.
    If no component intersects with the center box, returns the original mask.
    
    Pixels outside the largest connected component (if any) are set to 0.
    
    :param prediction: 2D numpy array of shape (H, W) containing predicted labels.
    :param center_percentage: Fraction of image dimensions used to define the center region.
    :return: Modified prediction mask.
    """
    H, W = prediction.shape
    # Define center box dimensions
    cx, cy = W / 2, H / 2
    box_w = W * center_percentage
    box_h = H * center_percentage
    # Coordinates for the center region: (x1, y1, x2, y2)
    x1 = int(cx - box_w / 2)
    y1 = int(cy - box_h / 2)
    x2 = int(cx + box_w / 2)
    y2 = int(cy + box_h / 2)
    
    # Create a binary mask: foreground (nonzero predictions) as 1.
    binary = (prediction != 0).astype(np.uint8)
    
    # Find connected components using 8-connectivity.
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
    
    selected_component = None
    max_area = 0

    # Iterate over components (skip label 0, which is the background).
    for label in range(1, num_labels):
        x, y, w, h, area = stats[label]
        # Check for intersection between the component bounding box and the center box.
        if x < x2 and (x + w) > x1 and y < y2 and (y + h) > y1:
            if area > max_area:
                max_area = area
                selected_component = label
    
    # If a valid component was found, apply its mask.
    if selected_component is not None:
        mask = (labels == selected_component).astype(np.uint8)
        modified_prediction = prediction * mask
        return modified_prediction
    else:
        # No component intersects with the center region; return original mask.
        return prediction

# -----------------------------------------------------------------------------
# 1) JIT-compiled helper to fuse softmax + argmax on GPU
# -----------------------------------------------------------------------------
@torch.jit.script
def top1_probs_and_preds(logits: torch.Tensor):
    # logits: [B, C, H, W]
    log_p = F.log_softmax(logits, dim=1)      # single log-softmax kernel
    max_lp, preds = log_p.max(dim=1)          # [B, H, W], [B, H, W]
    max_p = max_lp.exp()                      # convert log-probs → probs
    return max_p, preds                       # both still on GPU


# -----------------------------------------------------------------------------
# 2) Updated eval_minibatch
#    - defers loss reduction to GPU (no .item() per batch)
#    - uses top1_probs_and_preds to do softmax+argmax in one fused kernel
#    - returns the raw loss tensor + pixel count so the caller can accumulate
# -----------------------------------------------------------------------------
def eval_minibatch(data, net, criterion, calc_metrics, args, val_idx):
    """
    Evaluate a single minibatch of images through HRNet.

    Returns:
      assets        : dict of numpy arrays for predictions, attention maps, etc.
      iou_acc       : fast_hist result for this batch
      loss_tensor   : a single-element GPU tensor (sum-loss over pixels), or None
      batch_pixels  : int, number of pixels in the batch
    """
    # 1) clear GPU cache
    torch.cuda.empty_cache()

    # 2) set up scales
    scales = [args.default_scale]
    if args.multi_scale_inference:
        scales.extend(float(x) for x in args.extra_scales.split(','))
        if val_idx == 0:
            logx.msg(f'Using multi-scale inference with scales {scales}')
    if args.tta_extra_scales:
        scales.extend(float(x) for x in args.tta_extra_scales.split(','))

    # 3) unpack data & sanity checks
    images, gt_image, img_names, scale_float = data
    assert images.ndim == 4 and gt_image.ndim == 3
    assert images.shape[2:] == gt_image.shape[1:]
    batch_pixels = images.size(0) * images.size(2) * images.size(3)
    input_size   = images.size(2), images.size(3)

    # 4) prepare flip list
    flips = [1, 0] if args.do_flip else [0]

    # 5) forward all scales & flips
    with torch.no_grad():
        output = 0.0
        for flip in flips:
            for scale in scales:
                # a) flip if requested
                if flip:
                    img_in = flip_tensor(images, 3)
                else:
                    img_in = images

                # b) resize for multi-scale
                if scale != 1.0:
                    target = [round(s * scale) for s in input_size]
                    img_in = resize_tensor(img_in, target)

                # c) send to GPU
                inputs = {'images': img_in, 'gts': gt_image}
                inputs = {k: v.cuda() for k, v in inputs.items()}

                # d) network inference
                out_dict = net(inputs)
                p = out_dict['pred']
                if not cfg.MODEL.MSCALE:
                    out_dict[fmt_scale('pred', scale)] = p

                # e) resize back
                if scale != 1.0:
                    p = resize_tensor(p, input_size)

                # f) unflip & accumulate
                if flip:
                    output = output + flip_tensor(p, 3)
                else:
                    output = output + p

    # 6) average multi-scale+flip
    output = output / (len(scales) * len(flips))

    # 7) defer loss computation to GPU
    if calc_metrics:
        # note: this remains on GPU, no .item() here
        loss_tensor = criterion(output, gt_image.cuda()) * batch_pixels
    else:
        loss_tensor = None

    # 8) fused softmax+argmax on GPU, then one CPU transfer
    max_probs_gpu, preds_gpu = top1_probs_and_preds(output)
    max_probs   = max_probs_gpu.cpu().numpy()    # [B, H, W]
    predictions = preds_gpu.cpu().numpy()        # [B, H, W]
    # zero out anything below confidence threshold
    if cfg.CONF_THRESH > 0:
        predictions[max_probs < cfg.CONF_THRESH] = 0

    # 9) post-processing filter
    filtered = np.empty_like(predictions)

    # 9.1) optional softmax threshold
    if hasattr(args, 'conf_thresh') and args.conf_thresh > 0:
        preds_gpu[max_probs_gpu < args.conf_thresh] = 0  # force background
        predictions = preds_gpu.cpu().numpy()

    for i in range(predictions.shape[0]):
        filtered[i] = modify_prediction_mask(predictions[i], center_percentage=0.2)
    predictions = filtered

    # 10) assemble assets
    assets = {}
    for k, v in out_dict.items():
        if k.startswith('attn_'):
            assets[k] = v
        if k.startswith('pred_'):
            s = F.softmax(v, dim=1)
            _, pm = s.max(1)
            assets[k] = pm.cpu().numpy()

    assets['predictions'] = predictions
    assets['prob_mask']   = max_probs

    # 11) optional error mask
    if calc_metrics:
        assets['err_mask'] = calc_err_mask_all(
            predictions, gt_image.numpy(), cfg.DATASET.NUM_CLASSES
        )

    # 12) segmentation IoU accumulator
    iou_acc = fast_hist(
        predictions.flatten(),
        gt_image.numpy().flatten(),
        cfg.DATASET.NUM_CLASSES
    )

    return assets, iou_acc, loss_tensor, batch_pixels


def sliding_window_validation(
    data, net, criterion, calc_metrics, args, val_idx, crop_size=(512,512), stride_ratio=2/3
):
    """
    Perform validation using sliding window crop to reduce GPU memory usage.
    Args:
        data: batch from val_loader (images, labels, img_names, _)
        net: model
        criterion: loss function
        calc_metrics: bool, whether to compute metrics
        args: arguments
        val_idx: validation index
        crop_size: (h, w) crop size for sliding window
        stride_ratio: stride as a fraction of crop size (default 2/3)
    Returns:
        assets, iou_acc_batch, loss_tensor, batch_pixels
    """
    images, labels, img_names, _ = data
    device = next(net.parameters()).device
    img = images[0]  # batch size 1 for validation
    label = labels[0]
    img = img.unsqueeze(0).to(device)
    label = label.unsqueeze(0).to(device)

    # Get image dimensions
    _, H, W = img.shape[-3:]
    crop_h, crop_w = crop_size
    stride_h = int(crop_h * stride_ratio)
    stride_w = int(crop_w * stride_ratio)

    num_classes = cfg.DATASET.NUM_CLASSES

    # Prepare output tensors for accumulating probabilities and counting overlaps
    output_probs = torch.zeros((1, num_classes, H, W), device=device)
    count_mat = torch.zeros((1, 1, H, W), device=device)

    # Slide window over the image
    for y in range(0, H, stride_h):
        for x in range(0, W, stride_w):
            y1 = y
            x1 = x
            y2 = min(y1 + crop_h, H)
            x2 = min(x1 + crop_w, W)
            y1 = max(y2 - crop_h, 0)
            x1 = max(x2 - crop_w, 0)
            img_crop = img[..., y1:y2, x1:x2]
            inputs = {'images': img_crop}
            with torch.no_grad():
                logits = net(inputs)
                # If model returns a dict, extract the main logits tensor
                if isinstance(logits, dict):
                    if 'out' in logits:
                        logits = logits['out']
                    elif 'pred' in logits:
                        logits = logits['pred']
                    else:
                        logits = next(iter(logits.values()))
                # Compute softmax probabilities for the crop
                probs = torch.softmax(logits, dim=1)
            # Accumulate probabilities and count overlaps
            output_probs[..., y1:y2, x1:x2] += probs
            count_mat[..., y1:y2, x1:x2] += 1

    # Average overlapping areas to get the final probability map
    output_probs = output_probs / count_mat
    pred = output_probs.argmax(1)

    # Compute loss and metrics if required
    loss_tensor = torch.tensor(0.0, device=device)
    batch_pixels = H * W
    iou_acc_batch = np.zeros((num_classes, num_classes), dtype=np.int64)
    if calc_metrics:
        if criterion is not None:
            loss_tensor = criterion(output_probs, label)
        gt = label.cpu().numpy().flatten()
        pd = pred.cpu().numpy().flatten()
        for a, p in zip(gt, pd):
            if a < num_classes and p < num_classes:
                iou_acc_batch[a, p] += 1

    # Prepare assets dictionary for dumper compatibility
    assets = {
        'pred': pred.cpu(),  # Tensor with predicted class indices
        'prob': output_probs.cpu(),  # Tensor with class probabilities
        'prob_mask': output_probs.max(1)[0].cpu().numpy(),  # Max probability per pixel (for visualization)
        'predictions': pred.cpu().numpy(),  # Numpy array with predicted class indices
    }
    return assets, iou_acc_batch, loss_tensor, batch_pixels


def validate_topn(val_loader, net, criterion, optim, epoch, args):
    """
    Find worse case failures ...

    Only single GPU for now

    First pass = calculate TP, FP, FN pixels per image per class
      Take these stats and determine the top20 images to dump per class
    Second pass = dump all those selected images
    """
    assert args.bs_val == 1

    ######################################################################
    # First pass
    ######################################################################
    logx.msg('First pass')
    image_metrics = {}

    net.eval()
    val_loss = AverageMeter()
    iou_acc = 0
    t = time.time()
    for val_idx, data in enumerate(val_loader):
        # Time the network inference
        t_inference_start = time.time()
        assets, _iou_acc = eval_minibatch(data, net, criterion, val_loss, True, args, val_idx)
        t_inference_end = time.time()
        print("Inference time: {:.4f} sec".format(t_inference_end - t_inference_start))
        
        input_images, labels, img_names, _ = data
        
        fp, fn = metrics_per_image(_iou_acc)
        
        # Update metrics dictionary
        img_name = img_names[0]
        image_metrics[img_name] = (fp, fn)
        
        # Aggregate IoU metrics
        iou_acc += _iou_acc
    
        # Logging progress every 20 iterations
        if val_idx % 20 == 0:
            logx.msg(f'validating[Iter: {val_idx + 1} / {len(val_loader)}], it took {time.time() - t} seconds.')
            t = time.time()
        
        # Break out early if in test mode
        if val_idx > 20 and args.test_mode:
            break

    eval_metrics(iou_acc, args, net, optim, val_loss, epoch)

    ######################################################################
    # Find dump_topn worst failures from a pixel count perspective
    ######################################################################
    from collections import defaultdict
    worst_images = defaultdict(dict)
    class_to_images = defaultdict(dict)
    for classid in range(cfg.DATASET.NUM_CLASSES):
        tbl = {}
        for img_name in image_metrics.keys():
            fp, fn = image_metrics[img_name]
            fp = fp[classid]
            fn = fn[classid]
            tbl[img_name] = fp + fn
        worst = sorted(tbl, key=tbl.get, reverse=True)
        for img_name in worst[:args.dump_topn]:
            fail_pixels = tbl[img_name]
            worst_images[img_name][classid] = fail_pixels
            class_to_images[classid][img_name] = fail_pixels
    msg = str(worst_images)
    logx.msg(msg)

    # write out per-gpu jsons
    # barrier
    # make single table
    return val_loss.avg

"""
should be in the funtion before return,
but dont know what we are doing here,
and its broken

    ######################################################################
    # 2nd pass
    ######################################################################
    logx.msg('Second pass')
    attn_map = None

    for val_idx, data in enumerate(val_loader):
        in_image, gt_image, img_names, _ = data

        # Only process images that were identified in first pass
        if not args.dump_topn_all and img_names[0] not in worst_images:
            continue

        with torch.no_grad():
            inputs = in_image.cuda()
            inputs = {'images': inputs, 'gts': gt_image}

            if cfg.MODEL.MSCALE:
                output, attn_map = net(inputs)
            else:
              output = net(inputs)


        output = torch.nn.functional.softmax(output, dim=1)
        prob_mask, predictions = output.data.max(1)
        predictions = predictions.cpu()

        # this has shape [bs, h, w]
        img_name = img_names[0]
        for classid in worst_images[img_name].keys():

            err_mask = calc_err_mask(predictions.numpy(),
                                     gt_image.numpy(),
                                     cfg.DATASET.NUM_CLASSES,
                                     classid)

            class_name = cfg.DATASET_INST.trainid_to_name[classid]
            error_pixels = worst_images[img_name][classid]
            logx.msg(f'{img_name} {class_name}: {error_pixels}')
            img_names = [img_name + f'_{class_name}']

            to_dump = {'gt_images': gt_image,
                       'input_images': in_image,
                       'predictions': predictions.numpy(),
                       'err_mask': err_mask,
                       'prob_mask': prob_mask,
                       'img_names': img_names}

            if attn_map is not None:
                to_dump['attn_maps'] = attn_map

            # FIXME!
            # do_dump_images([to_dump])

    html_fn = os.path.join(args.result_dir, 'best_images',
                           'topn_failures.html')
    from utils.results_page import ResultsPage
    ip = ResultsPage('topn failures', html_fn)
    for classid in class_to_images:
        class_name = cfg.DATASET_INST.trainid_to_name[classid]
        img_dict = class_to_images[classid]
        for img_name in sorted(img_dict, key=img_dict.get, reverse=True):
            fail_pixels = class_to_images[classid][img_name]
            img_cls = f'{img_name}_{class_name}'
            pred_fn = f'{img_cls}_prediction.png'
            gt_fn = f'{img_cls}_gt.png'
            inp_fn = f'{img_cls}_input.png'
            err_fn = f'{img_cls}_err_mask.png'
            prob_fn = f'{img_cls}_prob_mask.png'
            img_label_pairs = [(pred_fn, 'pred'),
                               (gt_fn, 'gt'),
                               (inp_fn, 'input'),
                               (err_fn, 'errors'),
                               (prob_fn, 'prob')]
            ip.add_table(img_label_pairs,
                         table_heading=f'{class_name}-{fail_pixels}')
    ip.write_page()
"""
