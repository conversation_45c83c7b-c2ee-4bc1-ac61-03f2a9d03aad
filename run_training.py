#!/usr/bin/env python3
import re
import subprocess
import sys
import ast

def parse_config(config_path):
    """
    Parses a config file of the style:

    CMD: "python /path/to/train.py"
    HPARAMS: [
      {
       dataset: tal,
       cv: 0,
       crop_size: "512,512",
       scale_max: "1.5",
       ...
      },
    ]

    Returns:
      cmd (str): The command (e.g. "python /path/to/train.py")
      params (dict): Key/value of parameters (e.g. {"dataset": "tal", "cv": 0, ...})
    """
    cmd = None
    params = {}

    with open(config_path, 'r') as f:
        content = f.read()

    # Extract CMD line
    cmd_match = re.search(r'CMD:\s*"([^"]+)"', content)
    if cmd_match:
        cmd = cmd_match.group(1).strip()

    # Extract the dict block inside HPARAMS: [ {  ... } ]
    # We'll grab the first { ... } block for simplicity
    hparams_block = re.search(
        r'HPARAMS:\s*\[\s*\{([^}]+)\}', content, re.DOTALL
    )
    if hparams_block:
        # Inside we may have lines like:  dataset: tal,
        raw_hparams = hparams_block.group(1).strip().split('\n')
        for line in raw_hparams:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            # example line might be "dataset: tal,"
            if ':' in line:
                key, val = line.split(':', 1)
                key = key.strip()
                val = val.strip().rstrip(',')
                # Try to interpret booleans/lists/strings/integers/floats
                val = interpret_value(val)
                params[key] = val

    return cmd, params


def interpret_value(val):
    """
    Convert the raw string (e.g. 'true', '123', '1.5', "'some_string'", "[1,2]")
    into the proper Python type if possible.
    """
    val_lower = val.lower()
    # handle special booleans
    if val_lower == 'true':
        return True
    if val_lower == 'false':
        return False

    # handle list/tuple via ast
    if (val.startswith('[') and val.endswith(']')) or \
       (val.startswith('(') and val.endswith(')')):
        try:
            return ast.literal_eval(val)
        except:
            pass

    # handle quoted strings
    if (val.startswith('"') and val.endswith('"')) or \
       (val.startswith("'") and val.endswith("'")):
        return val[1:-1]

    # try numeric
    try:
        if '.' in val:
            as_float = float(val)
            # If it was something like "5.0", convert to int
            return int(as_float) if as_float.is_integer() else as_float
        else:
            return int(val)
    except ValueError:
        # fallback to string
        return val


def build_train_command(cmd, params):
    """
    Builds a command list to pass to subprocess, based on the 'train.py' arguments.
    cmd is the base command (e.g. "python /path/to/train.py").
    params is a dict of argument name -> value.

    Returns a list, e.g.:
      ["python", "/path/to/train.py", "--dataset=tal", "--cv=0", "--dump_assets", ...]
    """
    parts = cmd.split()
    for k, v in params.items():
        # For arguments defined as 'store_true' in train.py,
        # pass them with "--flag" only when True
        if v is True:
            # e.g. '--dump_all_images'
            parts.append(f'--{k}')
        elif v is False:
            # For 'store_true' flags, do nothing to disable
            pass
        else:
            # For everything else
            parts.append(f'--{k}={v}')
    return parts


def main():
    if len(sys.argv) < 2:
        print("Usage: python run_training.py /path/to/config.yaml")
        sys.exit(1)

    config_path = sys.argv[1]
    cmd, params = parse_config(config_path)
    if not cmd:
        print("No valid CMD line found in config.")
        sys.exit(1)

    train_cmd = build_train_command(cmd, params)
    print("Running:", " ".join(train_cmd))
    # Launch training
    subprocess.run(train_cmd)


if __name__ == "__main__":
    main()
