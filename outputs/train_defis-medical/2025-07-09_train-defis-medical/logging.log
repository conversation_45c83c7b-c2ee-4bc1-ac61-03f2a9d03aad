Torch version: 2.2, 2.2.2+cu118
n_scales [0.25, 1.0]
dataset = defis_medical
ignore_label = 255
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
all imgs 9
all imgs 165
Didn't find /mnt/data/projects/hrnet/data/uniform_centroids/defis_medical_cv0_tile128.json, so building it
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
Using RMI loss on training
Using Cross Entropy Loss on evaluation
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Model params = 72.1M
Starting training
Training Epoch 0
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 0], [iter 1 / 71], [train main loss -2.454399], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 2 / 71], [train main loss -2.703935], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 3 / 71], [train main loss -4.796213], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 4 / 71], [train main loss -5.831573], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 5 / 71], [train main loss -6.169032], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 6 / 71], [train main loss -5.754647], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 7 / 71], [train main loss -6.258977], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 8 / 71], [train main loss -7.029959], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 9 / 71], [train main loss -8.068868], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 10 / 71], [train main loss -8.612825], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 11 / 71], [train main loss -8.533881], [lr 0.005000] [batchtime 0.814]
[epoch 0], [iter 12 / 71], [train main loss -9.078245], [lr 0.005000] [batchtime 0.817]
[epoch 0], [iter 13 / 71], [train main loss -9.267043], [lr 0.005000] [batchtime 0.818]
[epoch 0], [iter 14 / 71], [train main loss -9.400771], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 15 / 71], [train main loss -9.592258], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 16 / 71], [train main loss -9.427781], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 17 / 71], [train main loss -9.595343], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 18 / 71], [train main loss -9.710232], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 19 / 71], [train main loss -9.922601], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 20 / 71], [train main loss -10.066077], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 21 / 71], [train main loss -10.059353], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 22 / 71], [train main loss -10.174152], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 23 / 71], [train main loss -10.438469], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 24 / 71], [train main loss -10.533980], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 25 / 71], [train main loss -10.709826], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 26 / 71], [train main loss -10.912310], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 27 / 71], [train main loss -10.855720], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 28 / 71], [train main loss -10.719263], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 29 / 71], [train main loss -10.615128], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 30 / 71], [train main loss -10.803912], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 31 / 71], [train main loss -10.769238], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 32 / 71], [train main loss -10.835043], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 33 / 71], [train main loss -10.874720], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 34 / 71], [train main loss -10.888868], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 35 / 71], [train main loss -10.808244], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 36 / 71], [train main loss -11.029138], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 37 / 71], [train main loss -11.173804], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 38 / 71], [train main loss -11.195525], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 39 / 71], [train main loss -11.096872], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 40 / 71], [train main loss -11.184611], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 41 / 71], [train main loss -11.300630], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 42 / 71], [train main loss -11.342295], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 43 / 71], [train main loss -11.373337], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 44 / 71], [train main loss -11.407831], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 45 / 71], [train main loss -11.483131], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 46 / 71], [train main loss -11.640546], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 47 / 71], [train main loss -11.706940], [lr 0.005000] [batchtime 0.825]
[epoch 0], [iter 48 / 71], [train main loss -11.853865], [lr 0.005000] [batchtime 0.825]
[epoch 0], [iter 49 / 71], [train main loss -11.929423], [lr 0.005000] [batchtime 0.825]
[epoch 0], [iter 50 / 71], [train main loss -11.900945], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 51 / 71], [train main loss -11.986977], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 52 / 71], [train main loss -12.001373], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 53 / 71], [train main loss -12.006067], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 54 / 71], [train main loss -11.970174], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 55 / 71], [train main loss -12.046539], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 56 / 71], [train main loss -12.097245], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 57 / 71], [train main loss -12.165739], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 58 / 71], [train main loss -12.118850], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 59 / 71], [train main loss -12.231042], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 60 / 71], [train main loss -12.297484], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 61 / 71], [train main loss -12.298560], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 62 / 71], [train main loss -12.292568], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 63 / 71], [train main loss -12.313228], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 64 / 71], [train main loss -12.352314], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 65 / 71], [train main loss -12.321439], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 66 / 71], [train main loss -12.373978], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 67 / 71], [train main loss -12.389748], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 68 / 71], [train main loss -12.411864], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 69 / 71], [train main loss -12.443800], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 70 / 71], [train main loss -12.499248], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 71 / 71], [train main loss -12.472103], [lr 0.005000] [batchtime 0.829]
End of training epoch 0, time: 68.48 sec

Torch version: 2.2, 2.2.2+cu118
n_scales [0.25, 1.0]
dataset = defis_medical
ignore_label = 255
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
all imgs 9
all imgs 165
Loading centroid file /mnt/data/projects/hrnet/data/uniform_centroids/defis_medical_cv0_tile128.json
Found 8 centroids
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
Using RMI loss on training
Using Cross Entropy Loss on evaluation
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Model params = 72.1M
Starting training
Training Epoch 0
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 0], [iter 1 / 71], [train main loss -3.998177], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 2 / 71], [train main loss -4.493600], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 3 / 71], [train main loss -5.682146], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 4 / 71], [train main loss -6.149630], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 5 / 71], [train main loss -7.222552], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 6 / 71], [train main loss -7.588071], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 7 / 71], [train main loss -8.317143], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 8 / 71], [train main loss -8.691083], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 9 / 71], [train main loss -9.257356], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 10 / 71], [train main loss -9.708825], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 11 / 71], [train main loss -9.875224], [lr 0.005000] [batchtime 0.816]
[epoch 0], [iter 12 / 71], [train main loss -10.069317], [lr 0.005000] [batchtime 0.818]
[epoch 0], [iter 13 / 71], [train main loss -10.305778], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 14 / 71], [train main loss -10.335885], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 15 / 71], [train main loss -10.534003], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 16 / 71], [train main loss -10.764958], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 17 / 71], [train main loss -10.591855], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 18 / 71], [train main loss -10.773530], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 19 / 71], [train main loss -10.793559], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 20 / 71], [train main loss -10.787953], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 21 / 71], [train main loss -11.041847], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 22 / 71], [train main loss -11.034801], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 23 / 71], [train main loss -10.908561], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 24 / 71], [train main loss -11.130094], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 25 / 71], [train main loss -11.212046], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 26 / 71], [train main loss -11.405327], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 27 / 71], [train main loss -11.284270], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 28 / 71], [train main loss -11.223618], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 29 / 71], [train main loss -11.305660], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 30 / 71], [train main loss -11.295658], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 31 / 71], [train main loss -11.233346], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 32 / 71], [train main loss -11.244118], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 33 / 71], [train main loss -11.319104], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 34 / 71], [train main loss -11.467933], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 35 / 71], [train main loss -11.676006], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 36 / 71], [train main loss -11.793514], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 37 / 71], [train main loss -11.782960], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 38 / 71], [train main loss -11.752303], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 39 / 71], [train main loss -11.637342], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 40 / 71], [train main loss -11.671346], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 41 / 71], [train main loss -11.717373], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 42 / 71], [train main loss -11.653180], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 43 / 71], [train main loss -11.689996], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 44 / 71], [train main loss -11.759026], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 45 / 71], [train main loss -11.784309], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 46 / 71], [train main loss -11.791656], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 47 / 71], [train main loss -11.834100], [lr 0.005000] [batchtime 0.828]
[epoch 0], [iter 48 / 71], [train main loss -11.858824], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 49 / 71], [train main loss -11.946292], [lr 0.005000] [batchtime 0.83]
[epoch 0], [iter 50 / 71], [train main loss -11.948487], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 51 / 71], [train main loss -12.025264], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 52 / 71], [train main loss -12.079104], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 53 / 71], [train main loss -12.157292], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 54 / 71], [train main loss -12.193124], [lr 0.005000] [batchtime 0.83]
[epoch 0], [iter 55 / 71], [train main loss -12.226154], [lr 0.005000] [batchtime 0.83]
[epoch 0], [iter 56 / 71], [train main loss -12.294262], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 57 / 71], [train main loss -12.407359], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 58 / 71], [train main loss -12.474056], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 59 / 71], [train main loss -12.517656], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 60 / 71], [train main loss -12.496863], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 61 / 71], [train main loss -12.524575], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 62 / 71], [train main loss -12.587633], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 63 / 71], [train main loss -12.609445], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 64 / 71], [train main loss -12.705778], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 65 / 71], [train main loss -12.661179], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 66 / 71], [train main loss -12.685780], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 67 / 71], [train main loss -12.678616], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 68 / 71], [train main loss -12.622747], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 69 / 71], [train main loss -12.601911], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 70 / 71], [train main loss -12.583855], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 71 / 71], [train main loss -12.616153], [lr 0.005000] [batchtime 0.832]
End of training epoch 0, time: 68.55 sec

Per-class metrics (percentages relative to ground-truth size):
  Id  Label         iU_1.0    TP (%GT)    FP (%GT)    FN (%GT)    FP (%Pred)    Precision    Recall
----  ----------  --------  ----------  ----------  ----------  ------------  -----------  --------
   0  Background       nan        0.00        0.00        0.00          0.00         0.00      0.00
   1  hole             nan        0.00        0.00        0.00          0.00         0.00      0.00
   2  strips           nan        0.00        0.00        0.00          0.00         0.00      0.00
   3  stain            nan        0.00        0.00        0.00          0.00         0.00      0.00
   4  line             nan        0.00        0.00        0.00          0.00         0.00      0.00
   5  knots            nan        0.00        0.00        0.00          0.00         0.00      0.00
   6  fiber            nan        0.00        0.00        0.00          0.00         0.00      0.00
   7  surface          nan        0.00        0.00        0.00          0.00         0.00      0.00
   8  elastic          nan        0.00        0.00        0.00          0.00         0.00      0.00
   9                   nan        0.00        0.00        0.00          0.00         0.00      0.00
Mean: nan
P | R (macro):  0.00 |  0.00
-----------------------------------------------------------------------------------------------------------
this : [epoch 0], [val loss 0.00000], [acc nan], [acc_cls nan], [mean_iu nan], [fwavacc 0.00000]
best : [epoch 0], [val loss 10000000000.00000], [acc 0.00000], [acc_cls 0.00000], [mean_iu 0.00000], [fwavacc -1.00000]
-----------------------------------------------------------------------------------------------------------
End of val epoch 0, time: 5.77 sec

Training Epoch 1
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 1], [iter 1 / 71], [train main loss -16.407063], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 2 / 71], [train main loss -16.439388], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 3 / 71], [train main loss -13.608345], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 4 / 71], [train main loss -13.752804], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 5 / 71], [train main loss -13.884808], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 6 / 71], [train main loss -13.084254], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 7 / 71], [train main loss -12.759108], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 8 / 71], [train main loss -12.146888], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 9 / 71], [train main loss -12.298959], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 10 / 71], [train main loss -11.935369], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 11 / 71], [train main loss -12.282205], [lr 0.004998] [batchtime 0.825]
[epoch 1], [iter 12 / 71], [train main loss -12.644323], [lr 0.004998] [batchtime 0.826]
[epoch 1], [iter 13 / 71], [train main loss -12.925021], [lr 0.004998] [batchtime 0.826]
[epoch 1], [iter 14 / 71], [train main loss -12.784983], [lr 0.004998] [batchtime 0.826]
[epoch 1], [iter 15 / 71], [train main loss -12.854007], [lr 0.004998] [batchtime 0.829]
[epoch 1], [iter 16 / 71], [train main loss -12.776408], [lr 0.004998] [batchtime 0.833]
[epoch 1], [iter 17 / 71], [train main loss -12.696022], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 18 / 71], [train main loss -12.686365], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 19 / 71], [train main loss -12.713705], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 20 / 71], [train main loss -12.457819], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 21 / 71], [train main loss -12.600041], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 22 / 71], [train main loss -12.417162], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 23 / 71], [train main loss -12.458139], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 24 / 71], [train main loss -12.496303], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 25 / 71], [train main loss -12.563662], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 26 / 71], [train main loss -12.644974], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 27 / 71], [train main loss -12.726190], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 28 / 71], [train main loss -12.694425], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 29 / 71], [train main loss -12.589514], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 30 / 71], [train main loss -12.519258], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 31 / 71], [train main loss -12.508187], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 32 / 71], [train main loss -12.494412], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 33 / 71], [train main loss -12.592217], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 34 / 71], [train main loss -12.616927], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 35 / 71], [train main loss -12.677671], [lr 0.004998] [batchtime 0.835]
[epoch 1], [iter 36 / 71], [train main loss -12.563207], [lr 0.004998] [batchtime 0.835]
[epoch 1], [iter 37 / 71], [train main loss -12.729235], [lr 0.004998] [batchtime 0.835]
[epoch 1], [iter 38 / 71], [train main loss -12.727393], [lr 0.004998] [batchtime 0.836]
[epoch 1], [iter 39 / 71], [train main loss -12.672850], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 40 / 71], [train main loss -12.669092], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 41 / 71], [train main loss -12.633702], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 42 / 71], [train main loss -12.777322], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 43 / 71], [train main loss -12.733424], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 44 / 71], [train main loss -12.763239], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 45 / 71], [train main loss -12.675320], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 46 / 71], [train main loss -12.806893], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 47 / 71], [train main loss -12.840161], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 48 / 71], [train main loss -12.719691], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 49 / 71], [train main loss -12.710453], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 50 / 71], [train main loss -12.830275], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 51 / 71], [train main loss -12.868053], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 52 / 71], [train main loss -12.747410], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 53 / 71], [train main loss -12.674238], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 54 / 71], [train main loss -12.725766], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 55 / 71], [train main loss -12.794525], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 56 / 71], [train main loss -12.855552], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 57 / 71], [train main loss -12.912050], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 58 / 71], [train main loss -13.011901], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 59 / 71], [train main loss -12.947803], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 60 / 71], [train main loss -12.914605], [lr 0.004998] [batchtime 0.838]
[epoch 1], [iter 61 / 71], [train main loss -12.962731], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 62 / 71], [train main loss -12.928174], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 63 / 71], [train main loss -12.909461], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 64 / 71], [train main loss -12.882665], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 65 / 71], [train main loss -12.902944], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 66 / 71], [train main loss -12.876485], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 67 / 71], [train main loss -12.907121], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 68 / 71], [train main loss -12.914566], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 69 / 71], [train main loss -12.928276], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 70 / 71], [train main loss -12.912010], [lr 0.004998] [batchtime 0.839]
[epoch 1], [iter 71 / 71], [train main loss -12.855954], [lr 0.004998] [batchtime 0.839]
End of training epoch 1, time: 60.98 sec

validating [Iter 1/9]
Torch version: 2.2, 2.2.2+cu118
n_scales [0.25, 1.0]
dataset = defis_medical
ignore_label = 255
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
all imgs 9
all imgs 165
Loading centroid file /mnt/data/projects/hrnet/data/uniform_centroids/defis_medical_cv0_tile128.json
Found 8 centroids
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
Using RMI loss on training
Using Cross Entropy Loss on evaluation
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Model params = 72.1M
Starting training
Training Epoch 0
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 0], [iter 1 / 71], [train main loss -2.634480], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 2 / 71], [train main loss -5.700018], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 3 / 71], [train main loss -4.778634], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 4 / 71], [train main loss -4.169136], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 5 / 71], [train main loss -5.440457], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 6 / 71], [train main loss -6.294327], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 7 / 71], [train main loss -7.153621], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 8 / 71], [train main loss -7.449420], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 9 / 71], [train main loss -8.452874], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 10 / 71], [train main loss -8.951231], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 11 / 71], [train main loss -8.919429], [lr 0.005000] [batchtime 0.816]
[epoch 0], [iter 12 / 71], [train main loss -9.100820], [lr 0.005000] [batchtime 0.819]
[epoch 0], [iter 13 / 71], [train main loss -9.567381], [lr 0.005000] [batchtime 0.82]
[epoch 0], [iter 14 / 71], [train main loss -9.777957], [lr 0.005000] [batchtime 0.821]
[epoch 0], [iter 15 / 71], [train main loss -9.851713], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 16 / 71], [train main loss -9.844901], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 17 / 71], [train main loss -10.221242], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 18 / 71], [train main loss -10.680562], [lr 0.005000] [batchtime 0.822]
[epoch 0], [iter 19 / 71], [train main loss -10.614908], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 20 / 71], [train main loss -10.666412], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 21 / 71], [train main loss -10.932116], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 22 / 71], [train main loss -10.926340], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 23 / 71], [train main loss -10.848531], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 24 / 71], [train main loss -10.902857], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 25 / 71], [train main loss -10.898785], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 26 / 71], [train main loss -11.095343], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 27 / 71], [train main loss -11.127183], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 28 / 71], [train main loss -11.135226], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 29 / 71], [train main loss -11.267036], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 30 / 71], [train main loss -11.516495], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 31 / 71], [train main loss -11.577857], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 32 / 71], [train main loss -11.623169], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 33 / 71], [train main loss -11.463736], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 34 / 71], [train main loss -11.437322], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 35 / 71], [train main loss -11.484302], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 36 / 71], [train main loss -11.528821], [lr 0.005000] [batchtime 0.823]
[epoch 0], [iter 37 / 71], [train main loss -11.556515], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 38 / 71], [train main loss -11.585160], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 39 / 71], [train main loss -11.535503], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 40 / 71], [train main loss -11.546210], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 41 / 71], [train main loss -11.610633], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 42 / 71], [train main loss -11.562019], [lr 0.005000] [batchtime 0.824]
[epoch 0], [iter 43 / 71], [train main loss -11.579400], [lr 0.005000] [batchtime 0.825]
[epoch 0], [iter 44 / 71], [train main loss -11.650705], [lr 0.005000] [batchtime 0.827]
[epoch 0], [iter 45 / 71], [train main loss -11.705134], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 46 / 71], [train main loss -11.749941], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 47 / 71], [train main loss -11.853020], [lr 0.005000] [batchtime 0.829]
[epoch 0], [iter 48 / 71], [train main loss -11.874333], [lr 0.005000] [batchtime 0.83]
[epoch 0], [iter 49 / 71], [train main loss -11.919040], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 50 / 71], [train main loss -11.905828], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 51 / 71], [train main loss -11.890138], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 52 / 71], [train main loss -11.783150], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 53 / 71], [train main loss -11.765792], [lr 0.005000] [batchtime 0.831]
[epoch 0], [iter 54 / 71], [train main loss -11.852397], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 55 / 71], [train main loss -11.928931], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 56 / 71], [train main loss -11.931618], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 57 / 71], [train main loss -12.051828], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 58 / 71], [train main loss -12.078999], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 59 / 71], [train main loss -12.039205], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 60 / 71], [train main loss -12.110422], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 61 / 71], [train main loss -12.087896], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 62 / 71], [train main loss -12.054945], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 63 / 71], [train main loss -12.121859], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 64 / 71], [train main loss -12.069783], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 65 / 71], [train main loss -12.172661], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 66 / 71], [train main loss -12.179196], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 67 / 71], [train main loss -12.191806], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 68 / 71], [train main loss -12.206461], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 69 / 71], [train main loss -12.205818], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 70 / 71], [train main loss -12.265850], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 71 / 71], [train main loss -12.322539], [lr 0.005000] [batchtime 0.833]
End of training epoch 0, time: 68.84 sec

Per-class metrics (percentages relative to ground-truth size):
  Id  Label         iU_1.0    TP (%GT)    FP (%GT)    FN (%GT)    FP (%Pred)    Precision    Recall
----  ----------  --------  ----------  ----------  ----------  ------------  -----------  --------
   0  Background       nan        0.00        0.00        0.00          0.00         0.00      0.00
   1  hole             nan        0.00        0.00        0.00          0.00         0.00      0.00
   2  strips           nan        0.00        0.00        0.00          0.00         0.00      0.00
   3  stain            nan        0.00        0.00        0.00          0.00         0.00      0.00
   4  line             nan        0.00        0.00        0.00          0.00         0.00      0.00
   5  knots            nan        0.00        0.00        0.00          0.00         0.00      0.00
   6  fiber            nan        0.00        0.00        0.00          0.00         0.00      0.00
   7  surface          nan        0.00        0.00        0.00          0.00         0.00      0.00
   8  elastic          nan        0.00        0.00        0.00          0.00         0.00      0.00
   9                   nan        0.00        0.00        0.00          0.00         0.00      0.00
Mean: nan
P | R (macro):  0.00 |  0.00
-----------------------------------------------------------------------------------------------------------
this : [epoch 0], [val loss 0.00000], [acc nan], [acc_cls nan], [mean_iu nan], [fwavacc 0.00000]
best : [epoch 0], [val loss 10000000000.00000], [acc 0.00000], [acc_cls 0.00000], [mean_iu 0.00000], [fwavacc -1.00000]
-----------------------------------------------------------------------------------------------------------
End of val epoch 0, time: 5.80 sec

Training Epoch 1
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 1], [iter 1 / 71], [train main loss -11.122955], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 2 / 71], [train main loss -11.817590], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 3 / 71], [train main loss -14.117007], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 4 / 71], [train main loss -13.995965], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 5 / 71], [train main loss -13.685345], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 6 / 71], [train main loss -13.527497], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 7 / 71], [train main loss -13.778990], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 8 / 71], [train main loss -14.402526], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 9 / 71], [train main loss -14.619527], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 10 / 71], [train main loss -15.035347], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 11 / 71], [train main loss -14.775156], [lr 0.004998] [batchtime 0.825]
[epoch 1], [iter 12 / 71], [train main loss -14.762872], [lr 0.004998] [batchtime 0.83]
[epoch 1], [iter 13 / 71], [train main loss -14.446001], [lr 0.004998] [batchtime 0.829]
[epoch 1], [iter 14 / 71], [train main loss -14.187163], [lr 0.004998] [batchtime 0.829]
[epoch 1], [iter 15 / 71], [train main loss -13.817203], [lr 0.004998] [batchtime 0.829]
[epoch 1], [iter 16 / 71], [train main loss -13.790648], [lr 0.004998] [batchtime 0.831]
[epoch 1], [iter 17 / 71], [train main loss -13.872043], [lr 0.004998] [batchtime 0.83]
[epoch 1], [iter 18 / 71], [train main loss -13.846027], [lr 0.004998] [batchtime 0.833]
[epoch 1], [iter 19 / 71], [train main loss -13.747939], [lr 0.004998] [batchtime 0.834]
[epoch 1], [iter 20 / 71], [train main loss -13.803256], [lr 0.004998] [batchtime 0.837]
[epoch 1], [iter 21 / 71], [train main loss -13.684322], [lr 0.004998] [batchtime 0.841]
[epoch 1], [iter 22 / 71], [train main loss -13.807495], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 23 / 71], [train main loss -13.866710], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 24 / 71], [train main loss -13.795551], [lr 0.004998] [batchtime 0.846]
[epoch 1], [iter 25 / 71], [train main loss -13.880107], [lr 0.004998] [batchtime 0.845]
[epoch 1], [iter 26 / 71], [train main loss -13.955020], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 27 / 71], [train main loss -13.761420], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 28 / 71], [train main loss -13.858927], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 29 / 71], [train main loss -13.871747], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 30 / 71], [train main loss -13.750080], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 31 / 71], [train main loss -13.654651], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 32 / 71], [train main loss -13.632405], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 33 / 71], [train main loss -13.567561], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 34 / 71], [train main loss -13.543886], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 35 / 71], [train main loss -13.533443], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 36 / 71], [train main loss -13.604546], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 37 / 71], [train main loss -13.414192], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 38 / 71], [train main loss -13.399834], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 39 / 71], [train main loss -13.352974], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 40 / 71], [train main loss -13.418829], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 41 / 71], [train main loss -13.483758], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 42 / 71], [train main loss -13.499661], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 43 / 71], [train main loss -13.416608], [lr 0.004998] [batchtime 0.845]
[epoch 1], [iter 44 / 71], [train main loss -13.277226], [lr 0.004998] [batchtime 0.846]
[epoch 1], [iter 45 / 71], [train main loss -13.253530], [lr 0.004998] [batchtime 0.847]
[epoch 1], [iter 46 / 71], [train main loss -13.232235], [lr 0.004998] [batchtime 0.848]
[epoch 1], [iter 47 / 71], [train main loss -13.161035], [lr 0.004998] [batchtime 0.848]
[epoch 1], [iter 48 / 71], [train main loss -13.141727], [lr 0.004998] [batchtime 0.849]
[epoch 1], [iter 49 / 71], [train main loss -13.201239], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 50 / 71], [train main loss -13.140944], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 51 / 71], [train main loss -13.129222], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 52 / 71], [train main loss -13.234320], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 53 / 71], [train main loss -13.109595], [lr 0.004998] [batchtime 0.855]
[epoch 1], [iter 54 / 71], [train main loss -13.038669], [lr 0.004998] [batchtime 0.855]
[epoch 1], [iter 55 / 71], [train main loss -13.067887], [lr 0.004998] [batchtime 0.856]
[epoch 1], [iter 56 / 71], [train main loss -13.047177], [lr 0.004998] [batchtime 0.857]
[epoch 1], [iter 57 / 71], [train main loss -13.085937], [lr 0.004998] [batchtime 0.858]
[epoch 1], [iter 58 / 71], [train main loss -13.025002], [lr 0.004998] [batchtime 0.858]
[epoch 1], [iter 59 / 71], [train main loss -13.039503], [lr 0.004998] [batchtime 0.859]
[epoch 1], [iter 60 / 71], [train main loss -13.030238], [lr 0.004998] [batchtime 0.86]
[epoch 1], [iter 61 / 71], [train main loss -13.052812], [lr 0.004998] [batchtime 0.861]
[epoch 1], [iter 62 / 71], [train main loss -13.061129], [lr 0.004998] [batchtime 0.861]
[epoch 1], [iter 63 / 71], [train main loss -13.028317], [lr 0.004998] [batchtime 0.862]
[epoch 1], [iter 64 / 71], [train main loss -13.074169], [lr 0.004998] [batchtime 0.862]
[epoch 1], [iter 65 / 71], [train main loss -13.050907], [lr 0.004998] [batchtime 0.863]
[epoch 1], [iter 66 / 71], [train main loss -13.055187], [lr 0.004998] [batchtime 0.863]
[epoch 1], [iter 67 / 71], [train main loss -13.095666], [lr 0.004998] [batchtime 0.864]
[epoch 1], [iter 68 / 71], [train main loss -13.177163], [lr 0.004998] [batchtime 0.864]
[epoch 1], [iter 69 / 71], [train main loss -13.200147], [lr 0.004998] [batchtime 0.865]
[epoch 1], [iter 70 / 71], [train main loss -13.156266], [lr 0.004998] [batchtime 0.866]
[epoch 1], [iter 71 / 71], [train main loss -13.130624], [lr 0.004998] [batchtime 0.866]
End of training epoch 1, time: 62.34 sec

validating [Iter 1/9]
Torch version: 2.2, 2.2.2+cu118
n_scales [0.25, 1.0]
dataset = defis_medical
ignore_label = 255
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
all imgs 9
all imgs 165
Loading centroid file /mnt/data/projects/hrnet/data/uniform_centroids/defis_medical_cv0_tile128.json
Found 8 centroids
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
Using RMI loss on training
Using Cross Entropy Loss on evaluation
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Model params = 72.1M
Starting training
Training Epoch 0
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 0], [iter 1 / 71], [train main loss -9.472898], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 2 / 71], [train main loss -6.323203], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 3 / 71], [train main loss -7.433300], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 4 / 71], [train main loss -7.261593], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 5 / 71], [train main loss -8.200702], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 6 / 71], [train main loss -8.486386], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 7 / 71], [train main loss -9.017268], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 8 / 71], [train main loss -9.527199], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 9 / 71], [train main loss -9.859072], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 10 / 71], [train main loss -10.551448], [lr 0.005000] [batchtime 0]
[epoch 0], [iter 11 / 71], [train main loss -10.893880], [lr 0.005000] [batchtime 0.826]
[epoch 0], [iter 12 / 71], [train main loss -11.292632], [lr 0.005000] [batchtime 0.83]
[epoch 0], [iter 13 / 71], [train main loss -11.438958], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 14 / 71], [train main loss -11.777181], [lr 0.005000] [batchtime 0.845]
[epoch 0], [iter 15 / 71], [train main loss -11.824696], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 16 / 71], [train main loss -12.009962], [lr 0.005000] [batchtime 0.838]
[epoch 0], [iter 17 / 71], [train main loss -11.849256], [lr 0.005000] [batchtime 0.836]
[epoch 0], [iter 18 / 71], [train main loss -11.799472], [lr 0.005000] [batchtime 0.834]
[epoch 0], [iter 19 / 71], [train main loss -11.588661], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 20 / 71], [train main loss -11.592388], [lr 0.005000] [batchtime 0.832]
[epoch 0], [iter 21 / 71], [train main loss -11.816174], [lr 0.005000] [batchtime 0.833]
[epoch 0], [iter 22 / 71], [train main loss -11.694588], [lr 0.005000] [batchtime 0.837]
[epoch 0], [iter 23 / 71], [train main loss -11.892264], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 24 / 71], [train main loss -11.918262], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 25 / 71], [train main loss -12.036106], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 26 / 71], [train main loss -12.293443], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 27 / 71], [train main loss -12.382153], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 28 / 71], [train main loss -12.522188], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 29 / 71], [train main loss -12.512623], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 30 / 71], [train main loss -12.445339], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 31 / 71], [train main loss -12.565522], [lr 0.005000] [batchtime 0.84]
[epoch 0], [iter 32 / 71], [train main loss -12.515329], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 33 / 71], [train main loss -12.508173], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 34 / 71], [train main loss -12.579285], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 35 / 71], [train main loss -12.527395], [lr 0.005000] [batchtime 0.843]
[epoch 0], [iter 36 / 71], [train main loss -12.629040], [lr 0.005000] [batchtime 0.843]
[epoch 0], [iter 37 / 71], [train main loss -12.638772], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 38 / 71], [train main loss -12.562372], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 39 / 71], [train main loss -12.401853], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 40 / 71], [train main loss -12.412514], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 41 / 71], [train main loss -12.373370], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 42 / 71], [train main loss -12.477018], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 43 / 71], [train main loss -12.379349], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 44 / 71], [train main loss -12.263440], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 45 / 71], [train main loss -12.274768], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 46 / 71], [train main loss -12.416637], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 47 / 71], [train main loss -12.404969], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 48 / 71], [train main loss -12.450802], [lr 0.005000] [batchtime 0.841]
[epoch 0], [iter 49 / 71], [train main loss -12.481583], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 50 / 71], [train main loss -12.424574], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 51 / 71], [train main loss -12.394751], [lr 0.005000] [batchtime 0.843]
[epoch 0], [iter 52 / 71], [train main loss -12.255629], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 53 / 71], [train main loss -12.254005], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 54 / 71], [train main loss -12.374158], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 55 / 71], [train main loss -12.306398], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 56 / 71], [train main loss -12.346725], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 57 / 71], [train main loss -12.381297], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 58 / 71], [train main loss -12.421316], [lr 0.005000] [batchtime 0.842]
[epoch 0], [iter 59 / 71], [train main loss -12.486994], [lr 0.005000] [batchtime 0.843]
[epoch 0], [iter 60 / 71], [train main loss -12.546069], [lr 0.005000] [batchtime 0.844]
[epoch 0], [iter 61 / 71], [train main loss -12.564669], [lr 0.005000] [batchtime 0.844]
[epoch 0], [iter 62 / 71], [train main loss -12.535298], [lr 0.005000] [batchtime 0.844]
[epoch 0], [iter 63 / 71], [train main loss -12.458366], [lr 0.005000] [batchtime 0.845]
[epoch 0], [iter 64 / 71], [train main loss -12.464523], [lr 0.005000] [batchtime 0.845]
[epoch 0], [iter 65 / 71], [train main loss -12.405444], [lr 0.005000] [batchtime 0.845]
[epoch 0], [iter 66 / 71], [train main loss -12.408954], [lr 0.005000] [batchtime 0.845]
[epoch 0], [iter 67 / 71], [train main loss -12.471557], [lr 0.005000] [batchtime 0.846]
[epoch 0], [iter 68 / 71], [train main loss -12.468171], [lr 0.005000] [batchtime 0.846]
[epoch 0], [iter 69 / 71], [train main loss -12.425899], [lr 0.005000] [batchtime 0.847]
[epoch 0], [iter 70 / 71], [train main loss -12.515936], [lr 0.005000] [batchtime 0.847]
[epoch 0], [iter 71 / 71], [train main loss -12.448154], [lr 0.005000] [batchtime 0.848]
End of training epoch 0, time: 70.17 sec

Per-class metrics (percentages relative to ground-truth size):
  Id  Label         iU_1.0    TP (%GT)    FP (%GT)    FN (%GT)    FP (%Pred)    Precision    Recall
----  ----------  --------  ----------  ----------  ----------  ------------  -----------  --------
   0  Background       nan        0.00        0.00        0.00          0.00         0.00      0.00
   1  hole             nan        0.00        0.00        0.00          0.00         0.00      0.00
   2  strips           nan        0.00        0.00        0.00          0.00         0.00      0.00
   3  stain            nan        0.00        0.00        0.00          0.00         0.00      0.00
   4  line             nan        0.00        0.00        0.00          0.00         0.00      0.00
   5  knots            nan        0.00        0.00        0.00          0.00         0.00      0.00
   6  fiber            nan        0.00        0.00        0.00          0.00         0.00      0.00
   7  surface          nan        0.00        0.00        0.00          0.00         0.00      0.00
   8  elastic          nan        0.00        0.00        0.00          0.00         0.00      0.00
   9                   nan        0.00        0.00        0.00          0.00         0.00      0.00
Mean: nan
P | R (macro):  0.00 |  0.00
-----------------------------------------------------------------------------------------------------------
this : [epoch 0], [val loss 0.00000], [acc nan], [acc_cls nan], [mean_iu nan], [fwavacc 0.00000]
best : [epoch 0], [val loss 10000000000.00000], [acc 0.00000], [acc_cls 0.00000], [mean_iu 0.00000], [fwavacc -1.00000]
-----------------------------------------------------------------------------------------------------------
End of val epoch 0, time: 5.92 sec

Training Epoch 1
Test:
0.7
Class Uniform Percentage: 0.7
Class Uniform items per Epoch: 165
cls 0 len 338427
cls 1 len 295
cls 2 len 1594
cls 3 len 3391
cls 4 len 814
cls 5 len 64
cls 6 len 216
cls 7 len 0
cls 8 len 0
cls 9 len 89
[epoch 1], [iter 1 / 71], [train main loss -15.809866], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 2 / 71], [train main loss -14.589790], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 3 / 71], [train main loss -13.264066], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 4 / 71], [train main loss -12.677693], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 5 / 71], [train main loss -11.690489], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 6 / 71], [train main loss -11.787034], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 7 / 71], [train main loss -12.160621], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 8 / 71], [train main loss -12.522899], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 9 / 71], [train main loss -12.208429], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 10 / 71], [train main loss -11.952551], [lr 0.004998] [batchtime 0]
[epoch 1], [iter 11 / 71], [train main loss -12.568787], [lr 0.004998] [batchtime 0.878]
[epoch 1], [iter 12 / 71], [train main loss -12.628160], [lr 0.004998] [batchtime 0.871]
[epoch 1], [iter 13 / 71], [train main loss -12.557897], [lr 0.004998] [batchtime 0.864]
[epoch 1], [iter 14 / 71], [train main loss -12.667273], [lr 0.004998] [batchtime 0.858]
[epoch 1], [iter 15 / 71], [train main loss -12.835473], [lr 0.004998] [batchtime 0.854]
[epoch 1], [iter 16 / 71], [train main loss -12.881484], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 17 / 71], [train main loss -12.662184], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 18 / 71], [train main loss -12.575598], [lr 0.004998] [batchtime 0.849]
[epoch 1], [iter 19 / 71], [train main loss -12.698892], [lr 0.004998] [batchtime 0.848]
[epoch 1], [iter 20 / 71], [train main loss -12.587136], [lr 0.004998] [batchtime 0.846]
[epoch 1], [iter 21 / 71], [train main loss -12.577183], [lr 0.004998] [batchtime 0.845]
[epoch 1], [iter 22 / 71], [train main loss -12.732054], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 23 / 71], [train main loss -12.690851], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 24 / 71], [train main loss -12.614316], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 25 / 71], [train main loss -12.659394], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 26 / 71], [train main loss -12.795471], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 27 / 71], [train main loss -12.619314], [lr 0.004998] [batchtime 0.843]
[epoch 1], [iter 28 / 71], [train main loss -12.631623], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 29 / 71], [train main loss -12.761764], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 30 / 71], [train main loss -12.678545], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 31 / 71], [train main loss -12.709616], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 32 / 71], [train main loss -12.726235], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 33 / 71], [train main loss -12.725246], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 34 / 71], [train main loss -12.901322], [lr 0.004998] [batchtime 0.842]
[epoch 1], [iter 35 / 71], [train main loss -12.836086], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 36 / 71], [train main loss -12.798586], [lr 0.004998] [batchtime 0.845]
[epoch 1], [iter 37 / 71], [train main loss -12.814599], [lr 0.004998] [batchtime 0.844]
[epoch 1], [iter 38 / 71], [train main loss -12.809373], [lr 0.004998] [batchtime 0.846]
[epoch 1], [iter 39 / 71], [train main loss -12.854541], [lr 0.004998] [batchtime 0.847]
[epoch 1], [iter 40 / 71], [train main loss -12.734290], [lr 0.004998] [batchtime 0.847]
[epoch 1], [iter 41 / 71], [train main loss -12.790074], [lr 0.004998] [batchtime 0.847]
[epoch 1], [iter 42 / 71], [train main loss -12.834165], [lr 0.004998] [batchtime 0.848]
[epoch 1], [iter 43 / 71], [train main loss -12.771989], [lr 0.004998] [batchtime 0.849]
[epoch 1], [iter 44 / 71], [train main loss -12.832893], [lr 0.004998] [batchtime 0.849]
[epoch 1], [iter 45 / 71], [train main loss -12.964209], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 46 / 71], [train main loss -13.006824], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 47 / 71], [train main loss -12.966616], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 48 / 71], [train main loss -12.910789], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 49 / 71], [train main loss -12.925249], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 50 / 71], [train main loss -12.923960], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 51 / 71], [train main loss -12.921444], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 52 / 71], [train main loss -12.853279], [lr 0.004998] [batchtime 0.85]
[epoch 1], [iter 53 / 71], [train main loss -12.879859], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 54 / 71], [train main loss -12.939356], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 55 / 71], [train main loss -12.950212], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 56 / 71], [train main loss -13.013816], [lr 0.004998] [batchtime 0.851]
[epoch 1], [iter 57 / 71], [train main loss -13.025250], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 58 / 71], [train main loss -13.009260], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 59 / 71], [train main loss -12.911606], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 60 / 71], [train main loss -12.915154], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 61 / 71], [train main loss -12.959764], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 62 / 71], [train main loss -12.978051], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 63 / 71], [train main loss -12.965989], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 64 / 71], [train main loss -12.993891], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 65 / 71], [train main loss -13.032181], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 66 / 71], [train main loss -12.971464], [lr 0.004998] [batchtime 0.852]
[epoch 1], [iter 67 / 71], [train main loss -12.973520], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 68 / 71], [train main loss -12.979067], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 69 / 71], [train main loss -12.931027], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 70 / 71], [train main loss -12.884499], [lr 0.004998] [batchtime 0.853]
[epoch 1], [iter 71 / 71], [train main loss -12.893507], [lr 0.004998] [batchtime 0.852]
End of training epoch 1, time: 61.67 sec

validating [Iter 1/9]
