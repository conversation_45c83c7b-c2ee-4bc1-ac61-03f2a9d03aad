Torch version: 2.2, 2.2.2+cu118
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu118
num_classes = 10
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu121
num_classes = 15
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
Torch version: 2.2, 2.2.2+cu121
num_classes = 15
=> init weights from normal distribution
=> loading pretrained model /mnt/data/projects/hrnet/data/seg_weights/hrnetv2_w48_imagenet_pretrained.pth
Trunk: hrnetv2
